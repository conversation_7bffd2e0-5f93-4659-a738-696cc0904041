'use client';

import React, { useCallback, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Play, 
  Pause, 
  Square, 
  Volume2, 
  VolumeX, 
  RotateCcw,
  Repeat,
  SkipBack,
  Ski<PERSON>Forward,
  Loader2,
  AlertCircle
} from 'lucide-react';

import { NaturalSoundPattern } from '@/types/NaturalPattern.types';

interface PlaybackState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isLooping: boolean;
  isMuted: boolean;
}

interface AudioControlsProps {
  pattern: NaturalSoundPattern;
  playbackState: PlaybackState;
  onPlayPause: () => void;
  onStop?: () => void;
  onSeek?: (time: number) => void;
  onVolumeChange: (volume: number) => void;
  onToggleLoop: () => void;
  onToggleMute: () => void;
  isEngineReady: boolean;
  isLoading?: boolean;
  error?: string | null;
  className?: string;
}

interface ProgressBarProps {
  currentTime: number;
  duration: number;
  onSeek?: (time: number) => void;
  isInteractive?: boolean;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  currentTime,
  duration,
  onSeek,
  isInteractive = true,
}) => {
  const progressRef = useRef<HTMLDivElement>(null);
  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

  const handleClick = useCallback((event: React.MouseEvent) => {
    if (!onSeek || !isInteractive || duration === 0) return;

    const rect = progressRef.current?.getBoundingClientRect();
    if (!rect) return;

    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;

    onSeek(Math.max(0, Math.min(newTime, duration)));
  }, [onSeek, isInteractive, duration]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-2">
      <div
        ref={progressRef}
        className={`
          relative h-2 bg-white/10 rounded-full overflow-hidden
          ${isInteractive ? 'cursor-pointer' : 'cursor-default'}
        `}
        onClick={handleClick}
      >
        {/* Progress fill */}
        <motion.div
          className="absolute left-0 top-0 h-full bg-gradient-to-r from-cosmic-400 to-cosmic-500 rounded-full"
          style={{ width: `${progress}%` }}
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.1 }}
        />

        {/* Hover indicator */}
        {isInteractive && (
          <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity">
            <div className="absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white rounded-full shadow-lg"
                 style={{ left: `${progress}%`, marginLeft: '-6px' }} />
          </div>
        )}
      </div>

      {/* Time display */}
      <div className="flex justify-between text-xs text-white/60">
        <span>{formatTime(currentTime)}</span>
        <span>{formatTime(duration)}</span>
      </div>
    </div>
  );
};

interface VolumeControlProps {
  volume: number;
  isMuted: boolean;
  onVolumeChange: (volume: number) => void;
  onToggleMute: () => void;
}

const VolumeControl: React.FC<VolumeControlProps> = ({
  volume,
  isMuted,
  onVolumeChange,
  onToggleMute,
}) => {
  const [showSlider, setShowSlider] = useState(false);
  const volumeRef = useRef<HTMLDivElement>(null);

  const handleVolumeClick = useCallback((event: React.MouseEvent) => {
    const rect = volumeRef.current?.getBoundingClientRect();
    if (!rect) return;

    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newVolume = Math.max(0, Math.min(percentage, 1));

    onVolumeChange(newVolume);
  }, [onVolumeChange]);

  const displayVolume = isMuted ? 0 : volume;
  const VolumeIcon = displayVolume === 0 ? VolumeX : Volume2;

  return (
    <div 
      className="relative flex items-center space-x-2"
      onMouseEnter={() => setShowSlider(true)}
      onMouseLeave={() => setShowSlider(false)}
    >
      <button
        onClick={onToggleMute}
        className="p-2 text-white/70 hover:text-white transition-colors"
        title={isMuted ? 'Unmute' : 'Mute'}
      >
        <VolumeIcon className="w-4 h-4" />
      </button>

      <AnimatePresence>
        {showSlider && (
          <motion.div
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: 80 }}
            exit={{ opacity: 0, width: 0 }}
            className="overflow-hidden"
          >
            <div
              ref={volumeRef}
              className="h-2 bg-white/10 rounded-full cursor-pointer relative"
              onClick={handleVolumeClick}
            >
              <motion.div
                className="absolute left-0 top-0 h-full bg-gradient-to-r from-cosmic-400 to-cosmic-500 rounded-full"
                style={{ width: `${displayVolume * 100}%` }}
                animate={{ width: `${displayVolume * 100}%` }}
                transition={{ duration: 0.1 }}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export const AudioControls: React.FC<AudioControlsProps> = ({
  pattern,
  playbackState,
  onPlayPause,
  onStop,
  onSeek,
  onVolumeChange,
  onToggleLoop,
  onToggleMute,
  isEngineReady,
  isLoading = false,
  error = null,
  className = '',
}) => {
  const { isPlaying, currentTime, duration, volume, isLooping, isMuted } = playbackState;

  const handleStop = useCallback(() => {
    if (onStop) {
      onStop();
    }
  }, [onStop]);

  return (
    <div className={`glass p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-white mb-1">
            Audio Controls
          </h3>
          <p className="text-white/60 text-sm">
            {pattern.name} • {pattern.category.replace('_', ' ')}
          </p>
        </div>

        {/* Status indicators */}
        <div className="flex items-center space-x-2">
          {isLoading && (
            <div className="flex items-center space-x-2 text-cosmic-400">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-xs">Loading...</span>
            </div>
          )}
          
          {error && (
            <div className="flex items-center space-x-2 text-red-400">
              <AlertCircle className="w-4 h-4" />
              <span className="text-xs">Error</span>
            </div>
          )}

          {!isEngineReady && !isLoading && !error && (
            <div className="text-white/40 text-xs">
              Click play to initialize audio
            </div>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <ProgressBar
          currentTime={currentTime}
          duration={duration}
          onSeek={onSeek}
          isInteractive={isEngineReady && !isLoading}
        />
      </div>

      {/* Main Controls */}
      <div className="flex items-center justify-center space-x-4 mb-4">
        {/* Skip Back */}
        <button
          onClick={() => onSeek?.(Math.max(0, currentTime - 10))}
          disabled={!isEngineReady || isLoading || !onSeek}
          className="p-2 text-white/70 hover:text-white disabled:text-white/30 disabled:cursor-not-allowed transition-colors"
          title="Skip back 10s"
        >
          <SkipBack className="w-5 h-5" />
        </button>

        {/* Play/Pause */}
        <motion.button
          onClick={onPlayPause}
          disabled={isLoading}
          className="p-4 bg-gradient-to-r from-cosmic-400 to-cosmic-500 rounded-full text-white 
                   hover:from-cosmic-500 hover:to-cosmic-600 disabled:opacity-50 disabled:cursor-not-allowed
                   transition-all duration-200 shadow-lg hover:shadow-xl"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          title={isPlaying ? 'Pause' : 'Play'}
        >
          {isLoading ? (
            <Loader2 className="w-6 h-6 animate-spin" />
          ) : isPlaying ? (
            <Pause className="w-6 h-6" />
          ) : (
            <Play className="w-6 h-6 ml-1" />
          )}
        </motion.button>

        {/* Stop */}
        {onStop && (
          <button
            onClick={handleStop}
            disabled={!isEngineReady || isLoading || !isPlaying}
            className="p-2 text-white/70 hover:text-white disabled:text-white/30 disabled:cursor-not-allowed transition-colors"
            title="Stop"
          >
            <Square className="w-5 h-5" />
          </button>
        )}

        {/* Skip Forward */}
        <button
          onClick={() => onSeek?.(Math.min(duration, currentTime + 10))}
          disabled={!isEngineReady || isLoading || !onSeek}
          className="p-2 text-white/70 hover:text-white disabled:text-white/30 disabled:cursor-not-allowed transition-colors"
          title="Skip forward 10s"
        >
          <SkipForward className="w-5 h-5" />
        </button>
      </div>

      {/* Secondary Controls */}
      <div className="flex items-center justify-between">
        {/* Loop Toggle */}
        <button
          onClick={onToggleLoop}
          className={`p-2 transition-colors ${
            isLooping 
              ? 'text-cosmic-400 hover:text-cosmic-300' 
              : 'text-white/70 hover:text-white'
          }`}
          title={isLooping ? 'Disable loop' : 'Enable loop'}
        >
          <Repeat className="w-4 h-4" />
        </button>

        {/* Volume Control */}
        <VolumeControl
          volume={volume}
          isMuted={isMuted}
          onVolumeChange={onVolumeChange}
          onToggleMute={onToggleMute}
        />

        {/* Reset */}
        <button
          onClick={() => onSeek?.(0)}
          disabled={!isEngineReady || isLoading || !onSeek}
          className="p-2 text-white/70 hover:text-white disabled:text-white/30 disabled:cursor-not-allowed transition-colors"
          title="Reset to beginning"
        >
          <RotateCcw className="w-4 h-4" />
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg"
        >
          <div className="flex items-center space-x-2 text-red-400">
            <AlertCircle className="w-4 h-4 flex-shrink-0" />
            <span className="text-sm">{error}</span>
          </div>
        </motion.div>
      )}
    </div>
  );
};
