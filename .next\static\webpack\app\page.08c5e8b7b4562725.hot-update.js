"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/explorer/PatternVisualization.tsx":
/*!**********************************************************!*\
  !*** ./src/components/explorer/PatternVisualization.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PatternVisualization: function() { return /* binding */ PatternVisualization; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Loader2,Maximize2,Minimize2,Settings,TrendingUp,Waves!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Loader2,Maximize2,Minimize2,Settings,TrendingUp,Waves!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Loader2,Maximize2,Minimize2,Settings,TrendingUp,Waves!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Loader2,Maximize2,Minimize2,Settings,TrendingUp,Waves!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Loader2,Maximize2,Minimize2,Settings,TrendingUp,Waves!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Loader2,Maximize2,Minimize2,Settings,TrendingUp,Waves!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/waves.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Loader2,Maximize2,Minimize2,Settings,TrendingUp,Waves!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Loader2,Maximize2,Minimize2,Settings,TrendingUp,Waves!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ PatternVisualization auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\nconst WaveformCanvas = (param)=>{\n    let { waveformData, width, height, color, isAnimated = false } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    useEffect(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        const drawWaveform = function() {\n            let offset = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n            ctx.clearRect(0, 0, width, height);\n            // Set up drawing style\n            ctx.strokeStyle = color;\n            ctx.lineWidth = 2;\n            ctx.lineCap = \"round\";\n            ctx.lineJoin = \"round\";\n            // Create gradient for visual appeal\n            const gradient = ctx.createLinearGradient(0, 0, 0, height);\n            gradient.addColorStop(0, color);\n            gradient.addColorStop(1, color + \"40\");\n            ctx.strokeStyle = gradient;\n            // Draw waveform\n            ctx.beginPath();\n            const sliceWidth = width / waveformData.length;\n            let x = 0;\n            for(let i = 0; i < waveformData.length; i++){\n                const dataIndex = isAnimated ? (i + offset) % waveformData.length : i;\n                const v = waveformData[dataIndex] * 0.5;\n                const y = v * height / 2 + height / 2;\n                if (i === 0) {\n                    ctx.moveTo(x, y);\n                } else {\n                    ctx.lineTo(x, y);\n                }\n                x += sliceWidth;\n            }\n            ctx.stroke();\n            // Add glow effect\n            ctx.shadowColor = color;\n            ctx.shadowBlur = 10;\n            ctx.stroke();\n            ctx.shadowBlur = 0;\n        };\n        if (isAnimated) {\n            let offset = 0;\n            const animate = ()=>{\n                drawWaveform(offset);\n                offset += 2;\n                animationRef.current = requestAnimationFrame(animate);\n            };\n            animate();\n        } else {\n            drawWaveform();\n        }\n        return ()=>{\n            if (animationRef.current) {\n                cancelAnimationFrame(animationRef.current);\n            }\n        };\n    }, [\n        waveformData,\n        width,\n        height,\n        color,\n        isAnimated\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        width: width,\n        height: height,\n        className: \"rounded-lg\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WaveformCanvas, \"X5bd7Q1XXg1keIMflMhOltk4wyU=\");\n_c = WaveformCanvas;\nconst SpectrumAnalyzer = (param)=>{\n    let { frequencyData, width, height, colorScheme } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    useEffect(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        ctx.clearRect(0, 0, width, height);\n        const barWidth = width / frequencyData.magnitudes.length;\n        const maxMagnitude = Math.max(...frequencyData.magnitudes);\n        frequencyData.magnitudes.forEach((magnitude, index)=>{\n            const barHeight = magnitude / maxMagnitude * height;\n            const x = index * barWidth;\n            const y = height - barHeight;\n            // Create gradient based on frequency\n            const hue = index / frequencyData.magnitudes.length * 240; // Blue to red\n            const gradient = ctx.createLinearGradient(0, y, 0, height);\n            if (colorScheme === \"cosmic\") {\n                gradient.addColorStop(0, \"hsl(\".concat(240 + hue * 0.5, \", 70%, 60%)\"));\n                gradient.addColorStop(1, \"hsl(\".concat(240 + hue * 0.5, \", 70%, 30%)\"));\n            } else {\n                gradient.addColorStop(0, \"hsl(\".concat(hue, \", 70%, 60%)\"));\n                gradient.addColorStop(1, \"hsl(\".concat(hue, \", 70%, 30%)\"));\n            }\n            ctx.fillStyle = gradient;\n            ctx.fillRect(x, y, barWidth - 1, barHeight);\n        });\n    }, [\n        frequencyData,\n        width,\n        height,\n        colorScheme\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        width: width,\n        height: height,\n        className: \"rounded-lg\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(SpectrumAnalyzer, \"UJgi7ynoup7eqypjnwyX/s32POg=\");\n_c1 = SpectrumAnalyzer;\nconst PatternVisualization = (param)=>{\n    let { pattern, analysisResults = [], isAnalyzing = false, className = \"\" } = param;\n    _s2();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        showWaveform: true,\n        showSpectrum: true,\n        showPatterns: true,\n        showHarmonics: false,\n        colorScheme: \"cosmic\",\n        sensitivity: 0.7\n    });\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [dimensions, setDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 800,\n        height: 200\n    });\n    // Update dimensions on resize\n    useEffect(()=>{\n        const updateDimensions = ()=>{\n            if (containerRef.current) {\n                const rect = containerRef.current.getBoundingClientRect();\n                setDimensions({\n                    width: rect.width - 32,\n                    height: isExpanded ? 400 : 200\n                });\n            }\n        };\n        updateDimensions();\n        window.addEventListener(\"resize\", updateDimensions);\n        return ()=>window.removeEventListener(\"resize\", updateDimensions);\n    }, [\n        isExpanded\n    ]);\n    // Color scheme mapping\n    const colorSchemes = {\n        cosmic: \"#8B5CF6\",\n        natural: \"#10B981\",\n        monochrome: \"#6B7280\"\n    };\n    const primaryColor = colorSchemes[config.colorScheme];\n    // Prepare visualization data\n    const visualizationData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _pattern_frequencyData, _pattern_frequencyData1;\n        return {\n            waveform: pattern.waveformData || new Float32Array(1024).map(()=>Math.random() * 2 - 1),\n            spectrum: {\n                frequencyBins: ((_pattern_frequencyData = pattern.frequencyData) === null || _pattern_frequencyData === void 0 ? void 0 : _pattern_frequencyData.frequencyBins) || [],\n                magnitudes: ((_pattern_frequencyData1 = pattern.frequencyData) === null || _pattern_frequencyData1 === void 0 ? void 0 : _pattern_frequencyData1.magnitudes) || []\n            },\n            harmonics: pattern.metadata.harmonicSeries || [],\n            patterns: analysisResults\n        };\n    }, [\n        pattern,\n        analysisResults\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"glass \".concat(className),\n        ref: containerRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-cosmic-500/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-5 h-5 text-cosmic-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Pattern Visualization\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: [\n                                            \"Real-time analysis of \",\n                                            pattern.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            isAnalyzing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-cosmic-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Analyzing...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-white/70 hover:text-white transition-colors\",\n                                title: \"Visualization settings\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsExpanded(!isExpanded),\n                                className: \"p-2 text-white/70 hover:text-white transition-colors\",\n                                title: isExpanded ? \"Minimize\" : \"Expand\",\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    className: \"border-b border-white/10 p-4 bg-white/5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-white/80\",\n                                        children: \"Display\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    [\n                                        {\n                                            key: \"showWaveform\",\n                                            label: \"Waveform\",\n                                            icon: _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                        },\n                                        {\n                                            key: \"showSpectrum\",\n                                            label: \"Spectrum\",\n                                            icon: _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                        },\n                                        {\n                                            key: \"showPatterns\",\n                                            label: \"Patterns\",\n                                            icon: _barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                        }\n                                    ].map((param)=>{\n                                        let { key, label, icon: Icon } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center space-x-2 cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: config[key],\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                [key]: e.target.checked\n                                                            })),\n                                                    className: \"rounded border-white/20 bg-white/10 text-cosmic-500 focus:ring-cosmic-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-3 h-3 text-white/60\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-white/80\",\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-white/80\",\n                                        children: \"Color Scheme\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: config.colorScheme,\n                                        onChange: (e)=>setConfig((prev)=>({\n                                                    ...prev,\n                                                    colorScheme: e.target.value\n                                                })),\n                                        className: \"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-sm text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"cosmic\",\n                                                children: \"Cosmic\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"natural\",\n                                                children: \"Natural\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"monochrome\",\n                                                children: \"Monochrome\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-white/80\",\n                                        children: \"Sensitivity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0.1\",\n                                        max: \"1\",\n                                        step: \"0.1\",\n                                        value: config.sensitivity,\n                                        onChange: (e)=>setConfig((prev)=>({\n                                                    ...prev,\n                                                    sensitivity: parseFloat(e.target.value)\n                                                })),\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-white/60\",\n                                        children: [\n                                            Math.round(config.sensitivity * 100),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 space-y-4\",\n                children: [\n                    config.showWaveform && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 text-white/60\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white/80\",\n                                        children: \"Waveform\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/20 rounded-lg p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WaveformCanvas, {\n                                    waveformData: visualizationData.waveform,\n                                    width: dimensions.width - 16,\n                                    height: 100,\n                                    color: primaryColor,\n                                    isAnimated: isAnalyzing\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, undefined),\n                    config.showSpectrum && visualizationData.spectrum.magnitudes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 text-white/60\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white/80\",\n                                        children: \"Frequency Spectrum\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-black/20 rounded-lg p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SpectrumAnalyzer, {\n                                    frequencyData: visualizationData.spectrum,\n                                    width: dimensions.width - 16,\n                                    height: 120,\n                                    colorScheme: config.colorScheme\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, undefined),\n                    config.showPatterns && analysisResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 text-white/60\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white/80\",\n                                        children: \"Detected Patterns\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                children: analysisResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: result.type || \"Pattern\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-cosmic-400\",\n                                                        children: [\n                                                            Math.round((result.confidence || 0) * 100),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-white/10 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-cosmic-400 to-cosmic-500 h-2 rounded-full transition-all duration-500\",\n                                                    style: {\n                                                        width: \"\".concat((result.confidence || 0) * 100, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, undefined),\n                    !isAnalyzing && analysisResults.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Loader2_Maximize2_Minimize2_Settings_TrendingUp_Waves_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-12 h-12 mx-auto mb-4 text-white/40\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-medium text-white/60 mb-2\",\n                                children: \"No Analysis Data\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/40 text-sm\",\n                                children: \"Pattern analysis will appear here when available\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternVisualization.tsx\",\n        lineNumber: 255,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(PatternVisualization, \"nGld7TS+amkqFAxqUjhfbcH8+Rg=\");\n_c2 = PatternVisualization;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"WaveformCanvas\");\n$RefreshReg$(_c1, \"SpectrumAnalyzer\");\n$RefreshReg$(_c2, \"PatternVisualization\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2V4cGxvcmVyL1BhdHRlcm5WaXN1YWxpemF0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXlEO0FBQ0Q7QUFZbEM7QUE0QnRCLE1BQU1jLGlCQUFnRDtRQUFDLEVBQ3JEQyxZQUFZLEVBQ1pDLEtBQUssRUFDTEMsTUFBTSxFQUNOQyxLQUFLLEVBQ0xDLGFBQWEsS0FBSyxFQUNuQjs7SUFDQyxNQUFNQyxZQUFZbkIsNkNBQU1BLENBQW9CO0lBQzVDLE1BQU1vQixlQUFlcEIsNkNBQU1BO0lBRTNCcUIsVUFBVTtRQUNSLE1BQU1DLFNBQVNILFVBQVVJLE9BQU87UUFDaEMsSUFBSSxDQUFDRCxRQUFRO1FBRWIsTUFBTUUsTUFBTUYsT0FBT0csVUFBVSxDQUFDO1FBQzlCLElBQUksQ0FBQ0QsS0FBSztRQUVWLE1BQU1FLGVBQWU7Z0JBQUNDLDBFQUFTO1lBQzdCSCxJQUFJSSxTQUFTLENBQUMsR0FBRyxHQUFHYixPQUFPQztZQUUzQix1QkFBdUI7WUFDdkJRLElBQUlLLFdBQVcsR0FBR1o7WUFDbEJPLElBQUlNLFNBQVMsR0FBRztZQUNoQk4sSUFBSU8sT0FBTyxHQUFHO1lBQ2RQLElBQUlRLFFBQVEsR0FBRztZQUVmLG9DQUFvQztZQUNwQyxNQUFNQyxXQUFXVCxJQUFJVSxvQkFBb0IsQ0FBQyxHQUFHLEdBQUcsR0FBR2xCO1lBQ25EaUIsU0FBU0UsWUFBWSxDQUFDLEdBQUdsQjtZQUN6QmdCLFNBQVNFLFlBQVksQ0FBQyxHQUFHbEIsUUFBUTtZQUNqQ08sSUFBSUssV0FBVyxHQUFHSTtZQUVsQixnQkFBZ0I7WUFDaEJULElBQUlZLFNBQVM7WUFDYixNQUFNQyxhQUFhdEIsUUFBUUQsYUFBYXdCLE1BQU07WUFDOUMsSUFBSUMsSUFBSTtZQUVSLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJMUIsYUFBYXdCLE1BQU0sRUFBRUUsSUFBSztnQkFDNUMsTUFBTUMsWUFBWXZCLGFBQWEsQ0FBQ3NCLElBQUliLE1BQUssSUFBS2IsYUFBYXdCLE1BQU0sR0FBR0U7Z0JBQ3BFLE1BQU1FLElBQUk1QixZQUFZLENBQUMyQixVQUFVLEdBQUc7Z0JBQ3BDLE1BQU1FLElBQUksSUFBSzNCLFNBQVMsSUFBTUEsU0FBUztnQkFFdkMsSUFBSXdCLE1BQU0sR0FBRztvQkFDWGhCLElBQUlvQixNQUFNLENBQUNMLEdBQUdJO2dCQUNoQixPQUFPO29CQUNMbkIsSUFBSXFCLE1BQU0sQ0FBQ04sR0FBR0k7Z0JBQ2hCO2dCQUVBSixLQUFLRjtZQUNQO1lBRUFiLElBQUlzQixNQUFNO1lBRVYsa0JBQWtCO1lBQ2xCdEIsSUFBSXVCLFdBQVcsR0FBRzlCO1lBQ2xCTyxJQUFJd0IsVUFBVSxHQUFHO1lBQ2pCeEIsSUFBSXNCLE1BQU07WUFDVnRCLElBQUl3QixVQUFVLEdBQUc7UUFDbkI7UUFFQSxJQUFJOUIsWUFBWTtZQUNkLElBQUlTLFNBQVM7WUFDYixNQUFNc0IsVUFBVTtnQkFDZHZCLGFBQWFDO2dCQUNiQSxVQUFVO2dCQUNWUCxhQUFhRyxPQUFPLEdBQUcyQixzQkFBc0JEO1lBQy9DO1lBQ0FBO1FBQ0YsT0FBTztZQUNMdkI7UUFDRjtRQUVBLE9BQU87WUFDTCxJQUFJTixhQUFhRyxPQUFPLEVBQUU7Z0JBQ3hCNEIscUJBQXFCL0IsYUFBYUcsT0FBTztZQUMzQztRQUNGO0lBQ0YsR0FBRztRQUFDVDtRQUFjQztRQUFPQztRQUFRQztRQUFPQztLQUFXO0lBRW5ELHFCQUNFLDhEQUFDSTtRQUNDOEIsS0FBS2pDO1FBQ0xKLE9BQU9BO1FBQ1BDLFFBQVFBO1FBQ1JxQyxXQUFVOzs7Ozs7QUFHaEI7R0F2Rk14QztLQUFBQTtBQW1HTixNQUFNeUMsbUJBQW9EO1FBQUMsRUFDekRDLGFBQWEsRUFDYnhDLEtBQUssRUFDTEMsTUFBTSxFQUNOd0MsV0FBVyxFQUNaOztJQUNDLE1BQU1yQyxZQUFZbkIsNkNBQU1BLENBQW9CO0lBRTVDcUIsVUFBVTtRQUNSLE1BQU1DLFNBQVNILFVBQVVJLE9BQU87UUFDaEMsSUFBSSxDQUFDRCxRQUFRO1FBRWIsTUFBTUUsTUFBTUYsT0FBT0csVUFBVSxDQUFDO1FBQzlCLElBQUksQ0FBQ0QsS0FBSztRQUVWQSxJQUFJSSxTQUFTLENBQUMsR0FBRyxHQUFHYixPQUFPQztRQUUzQixNQUFNeUMsV0FBVzFDLFFBQVF3QyxjQUFjRyxVQUFVLENBQUNwQixNQUFNO1FBQ3hELE1BQU1xQixlQUFlQyxLQUFLQyxHQUFHLElBQUlOLGNBQWNHLFVBQVU7UUFFekRILGNBQWNHLFVBQVUsQ0FBQ0ksT0FBTyxDQUFDLENBQUNDLFdBQVdDO1lBQzNDLE1BQU1DLFlBQVksWUFBYU4sZUFBZ0IzQztZQUMvQyxNQUFNdUIsSUFBSXlCLFFBQVFQO1lBQ2xCLE1BQU1kLElBQUkzQixTQUFTaUQ7WUFFbkIscUNBQXFDO1lBQ3JDLE1BQU1DLE1BQU0sUUFBU1gsY0FBY0csVUFBVSxDQUFDcEIsTUFBTSxHQUFJLEtBQUssY0FBYztZQUMzRSxNQUFNTCxXQUFXVCxJQUFJVSxvQkFBb0IsQ0FBQyxHQUFHUyxHQUFHLEdBQUczQjtZQUVuRCxJQUFJd0MsZ0JBQWdCLFVBQVU7Z0JBQzVCdkIsU0FBU0UsWUFBWSxDQUFDLEdBQUcsT0FBdUIsT0FBaEIsTUFBTStCLE1BQU0sS0FBSTtnQkFDaERqQyxTQUFTRSxZQUFZLENBQUMsR0FBRyxPQUF1QixPQUFoQixNQUFNK0IsTUFBTSxLQUFJO1lBQ2xELE9BQU87Z0JBQ0xqQyxTQUFTRSxZQUFZLENBQUMsR0FBRyxPQUFXLE9BQUorQixLQUFJO2dCQUNwQ2pDLFNBQVNFLFlBQVksQ0FBQyxHQUFHLE9BQVcsT0FBSitCLEtBQUk7WUFDdEM7WUFFQTFDLElBQUkyQyxTQUFTLEdBQUdsQztZQUNoQlQsSUFBSTRDLFFBQVEsQ0FBQzdCLEdBQUdJLEdBQUdjLFdBQVcsR0FBR1E7UUFDbkM7SUFDRixHQUFHO1FBQUNWO1FBQWV4QztRQUFPQztRQUFRd0M7S0FBWTtJQUU5QyxxQkFDRSw4REFBQ2xDO1FBQ0M4QixLQUFLakM7UUFDTEosT0FBT0E7UUFDUEMsUUFBUUE7UUFDUnFDLFdBQVU7Ozs7OztBQUdoQjtJQWxETUM7TUFBQUE7QUFvREMsTUFBTWUsdUJBQTREO1FBQUMsRUFDeEVDLE9BQU8sRUFDUEMsa0JBQWtCLEVBQUUsRUFDcEJDLGNBQWMsS0FBSyxFQUNuQm5CLFlBQVksRUFBRSxFQUNmOztJQUNDLE1BQU0sQ0FBQ29CLFFBQVFDLFVBQVUsR0FBR3pFLCtDQUFRQSxDQUFzQjtRQUN4RDBFLGNBQWM7UUFDZEMsY0FBYztRQUNkQyxjQUFjO1FBQ2RDLGVBQWU7UUFDZnRCLGFBQWE7UUFDYnVCLGFBQWE7SUFDZjtJQUNBLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHaEYsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDaUYsY0FBY0MsZ0JBQWdCLEdBQUdsRiwrQ0FBUUEsQ0FBQztJQUVqRCxNQUFNbUYsZUFBZXBGLDZDQUFNQSxDQUFpQjtJQUM1QyxNQUFNLENBQUNxRixZQUFZQyxjQUFjLEdBQUdyRiwrQ0FBUUEsQ0FBQztRQUFFYyxPQUFPO1FBQUtDLFFBQVE7SUFBSTtJQUV2RSw4QkFBOEI7SUFDOUJLLFVBQVU7UUFDUixNQUFNa0UsbUJBQW1CO1lBQ3ZCLElBQUlILGFBQWE3RCxPQUFPLEVBQUU7Z0JBQ3hCLE1BQU1pRSxPQUFPSixhQUFhN0QsT0FBTyxDQUFDa0UscUJBQXFCO2dCQUN2REgsY0FBYztvQkFDWnZFLE9BQU95RSxLQUFLekUsS0FBSyxHQUFHO29CQUNwQkMsUUFBUWdFLGFBQWEsTUFBTTtnQkFDN0I7WUFDRjtRQUNGO1FBRUFPO1FBQ0FHLE9BQU9DLGdCQUFnQixDQUFDLFVBQVVKO1FBQ2xDLE9BQU8sSUFBTUcsT0FBT0UsbUJBQW1CLENBQUMsVUFBVUw7SUFDcEQsR0FBRztRQUFDUDtLQUFXO0lBRWYsdUJBQXVCO0lBQ3ZCLE1BQU1hLGVBQWU7UUFDbkJDLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxZQUFZO0lBQ2Q7SUFFQSxNQUFNQyxlQUFlSixZQUFZLENBQUNwQixPQUFPakIsV0FBVyxDQUFDO0lBRXJELDZCQUE2QjtJQUM3QixNQUFNMEMsb0JBQW9CaEcsOENBQU9BLENBQUM7WUFJYm9FLHdCQUNIQTtRQUpoQixPQUFPO1lBQ0w2QixVQUFVN0IsUUFBUXhELFlBQVksSUFBSSxJQUFJc0YsYUFBYSxNQUFNQyxHQUFHLENBQUMsSUFBTXpDLEtBQUswQyxNQUFNLEtBQUssSUFBSTtZQUN2RkMsVUFBVTtnQkFDUkMsZUFBZWxDLEVBQUFBLHlCQUFBQSxRQUFRZixhQUFhLGNBQXJCZSw2Q0FBQUEsdUJBQXVCa0MsYUFBYSxLQUFJLEVBQUU7Z0JBQ3pEOUMsWUFBWVksRUFBQUEsMEJBQUFBLFFBQVFmLGFBQWEsY0FBckJlLDhDQUFBQSx3QkFBdUJaLFVBQVUsS0FBSSxFQUFFO1lBQ3JEO1lBQ0ErQyxXQUFXbkMsUUFBUW9DLFFBQVEsQ0FBQ0MsY0FBYyxJQUFJLEVBQUU7WUFDaERDLFVBQVVyQztRQUNaO0lBQ0YsR0FBRztRQUFDRDtRQUFTQztLQUFnQjtJQUU3QixxQkFDRSw4REFBQ3NDO1FBQUl4RCxXQUFXLFNBQW1CLE9BQVZBO1FBQWFELEtBQUtnQzs7MEJBRXpDLDhEQUFDeUI7Z0JBQUl4RCxXQUFVOztrQ0FDYiw4REFBQ3dEO3dCQUFJeEQsV0FBVTs7MENBQ2IsOERBQUN3RDtnQ0FBSXhELFdBQVU7MENBQ2IsNEVBQUMvQyxvSkFBUUE7b0NBQUMrQyxXQUFVOzs7Ozs7Ozs7OzswQ0FFdEIsOERBQUN3RDs7a0RBQ0MsOERBQUNDO3dDQUFHekQsV0FBVTtrREFBbUM7Ozs7OztrREFHakQsOERBQUMwRDt3Q0FBRTFELFdBQVU7OzRDQUF3Qjs0Q0FDWmlCLFFBQVEwQyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUt6Qyw4REFBQ0g7d0JBQUl4RCxXQUFVOzs0QkFDWm1CLDZCQUNDLDhEQUFDcUM7Z0NBQUl4RCxXQUFVOztrREFDYiw4REFBQ3pDLG9KQUFPQTt3Q0FBQ3lDLFdBQVU7Ozs7OztrREFDbkIsOERBQUM0RDt3Q0FBSzVELFdBQVU7a0RBQVU7Ozs7Ozs7Ozs7OzswQ0FJOUIsOERBQUM2RDtnQ0FDQ0MsU0FBUyxJQUFNaEMsZ0JBQWdCLENBQUNEO2dDQUNoQzdCLFdBQVU7Z0NBQ1YrRCxPQUFNOzBDQUVOLDRFQUFDekcsb0pBQVFBO29DQUFDMEMsV0FBVTs7Ozs7Ozs7Ozs7MENBR3RCLDhEQUFDNkQ7Z0NBQ0NDLFNBQVMsSUFBTWxDLGNBQWMsQ0FBQ0Q7Z0NBQzlCM0IsV0FBVTtnQ0FDVitELE9BQU9wQyxhQUFhLGFBQWE7MENBRWhDQSwyQkFDQyw4REFBQ3RFLG9KQUFTQTtvQ0FBQzJDLFdBQVU7Ozs7OzhEQUVyQiw4REFBQzVDLG9KQUFTQTtvQ0FBQzRDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU83Qiw4REFBQ2pELDBEQUFlQTswQkFDYjhFLDhCQUNDLDhEQUFDL0UsaURBQU1BLENBQUMwRyxHQUFHO29CQUNUUSxTQUFTO3dCQUFFQyxTQUFTO3dCQUFHdEcsUUFBUTtvQkFBRTtvQkFDakNpQyxTQUFTO3dCQUFFcUUsU0FBUzt3QkFBR3RHLFFBQVE7b0JBQU87b0JBQ3RDdUcsTUFBTTt3QkFBRUQsU0FBUzt3QkFBR3RHLFFBQVE7b0JBQUU7b0JBQzlCcUMsV0FBVTs4QkFFViw0RUFBQ3dEO3dCQUFJeEQsV0FBVTs7MENBRWIsOERBQUN3RDtnQ0FBSXhELFdBQVU7O2tEQUNiLDhEQUFDbUU7d0NBQU1uRSxXQUFVO2tEQUFvQzs7Ozs7O29DQUNwRDt3Q0FDQzs0Q0FBRW9FLEtBQUs7NENBQWdCRCxPQUFPOzRDQUFZRSxNQUFNbkgsb0pBQUtBO3dDQUFDO3dDQUN0RDs0Q0FBRWtILEtBQUs7NENBQWdCRCxPQUFPOzRDQUFZRSxNQUFNckgscUpBQVNBO3dDQUFDO3dDQUMxRDs0Q0FBRW9ILEtBQUs7NENBQWdCRCxPQUFPOzRDQUFZRSxNQUFNbEgscUpBQVVBO3dDQUFDO3FDQUM1RCxDQUFDNkYsR0FBRyxDQUFDOzRDQUFDLEVBQUVvQixHQUFHLEVBQUVELEtBQUssRUFBRUUsTUFBTUMsSUFBSSxFQUFFOzZEQUMvQiw4REFBQ0g7NENBQWdCbkUsV0FBVTs7OERBQ3pCLDhEQUFDdUU7b0RBQ0NDLE1BQUs7b0RBQ0xDLFNBQVNyRCxNQUFNLENBQUNnRCxJQUFpQztvREFDakRNLFVBQVUsQ0FBQ0MsSUFBTXRELFVBQVV1RCxDQUFBQSxPQUFTO2dFQUFFLEdBQUdBLElBQUk7Z0VBQUUsQ0FBQ1IsSUFBSSxFQUFFTyxFQUFFRSxNQUFNLENBQUNKLE9BQU87NERBQUM7b0RBQ3ZFekUsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDc0U7b0RBQUt0RSxXQUFVOzs7Ozs7OERBQ2hCLDhEQUFDNEQ7b0RBQUs1RCxXQUFVOzhEQUF5Qm1FOzs7Ozs7OzJDQVIvQkM7Ozs7Ozs7Ozs7OzswQ0FjaEIsOERBQUNaO2dDQUFJeEQsV0FBVTs7a0RBQ2IsOERBQUNtRTt3Q0FBTW5FLFdBQVU7a0RBQW9DOzs7Ozs7a0RBQ3JELDhEQUFDOEU7d0NBQ0NDLE9BQU8zRCxPQUFPakIsV0FBVzt3Q0FDekJ1RSxVQUFVLENBQUNDLElBQU10RCxVQUFVdUQsQ0FBQUEsT0FBUztvREFBRSxHQUFHQSxJQUFJO29EQUFFekUsYUFBYXdFLEVBQUVFLE1BQU0sQ0FBQ0UsS0FBSztnREFBUTt3Q0FDbEYvRSxXQUFVOzswREFFViw4REFBQ2dGO2dEQUFPRCxPQUFNOzBEQUFTOzs7Ozs7MERBQ3ZCLDhEQUFDQztnREFBT0QsT0FBTTswREFBVTs7Ozs7OzBEQUN4Qiw4REFBQ0M7Z0RBQU9ELE9BQU07MERBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLL0IsOERBQUN2QjtnQ0FBSXhELFdBQVU7O2tEQUNiLDhEQUFDbUU7d0NBQU1uRSxXQUFVO2tEQUFvQzs7Ozs7O2tEQUNyRCw4REFBQ3VFO3dDQUNDQyxNQUFLO3dDQUNMUyxLQUFJO3dDQUNKekUsS0FBSTt3Q0FDSjBFLE1BQUs7d0NBQ0xILE9BQU8zRCxPQUFPTSxXQUFXO3dDQUN6QmdELFVBQVUsQ0FBQ0MsSUFBTXRELFVBQVV1RCxDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUVsRCxhQUFheUQsV0FBV1IsRUFBRUUsTUFBTSxDQUFDRSxLQUFLO2dEQUFFO3dDQUN2Ri9FLFdBQVU7Ozs7OztrREFFWiw4REFBQzREO3dDQUFLNUQsV0FBVTs7NENBQXlCTyxLQUFLNkUsS0FBSyxDQUFDaEUsT0FBT00sV0FBVyxHQUFHOzRDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFReEYsOERBQUM4QjtnQkFBSXhELFdBQVU7O29CQUVab0IsT0FBT0UsWUFBWSxrQkFDbEIsOERBQUN4RSxpREFBTUEsQ0FBQzBHLEdBQUc7d0JBQ1RRLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUczRSxHQUFHO3dCQUFHO3dCQUM3Qk0sU0FBUzs0QkFBRXFFLFNBQVM7NEJBQUczRSxHQUFHO3dCQUFFO3dCQUM1QlUsV0FBVTs7MENBRVYsOERBQUN3RDtnQ0FBSXhELFdBQVU7O2tEQUNiLDhEQUFDOUMsb0pBQUtBO3dDQUFDOEMsV0FBVTs7Ozs7O2tEQUNqQiw4REFBQzREO3dDQUFLNUQsV0FBVTtrREFBb0M7Ozs7Ozs7Ozs7OzswQ0FFdEQsOERBQUN3RDtnQ0FBSXhELFdBQVU7MENBQ2IsNEVBQUN4QztvQ0FDQ0MsY0FBY29GLGtCQUFrQkMsUUFBUTtvQ0FDeENwRixPQUFPc0UsV0FBV3RFLEtBQUssR0FBRztvQ0FDMUJDLFFBQVE7b0NBQ1JDLE9BQU9nRjtvQ0FDUC9FLFlBQVlzRDs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBT25CQyxPQUFPRyxZQUFZLElBQUlzQixrQkFBa0JLLFFBQVEsQ0FBQzdDLFVBQVUsQ0FBQ3BCLE1BQU0sR0FBRyxtQkFDckUsOERBQUNuQyxpREFBTUEsQ0FBQzBHLEdBQUc7d0JBQ1RRLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUczRSxHQUFHO3dCQUFHO3dCQUM3Qk0sU0FBUzs0QkFBRXFFLFNBQVM7NEJBQUczRSxHQUFHO3dCQUFFO3dCQUM1QlUsV0FBVTs7MENBRVYsOERBQUN3RDtnQ0FBSXhELFdBQVU7O2tEQUNiLDhEQUFDaEQscUpBQVNBO3dDQUFDZ0QsV0FBVTs7Ozs7O2tEQUNyQiw4REFBQzREO3dDQUFLNUQsV0FBVTtrREFBb0M7Ozs7Ozs7Ozs7OzswQ0FFdEQsOERBQUN3RDtnQ0FBSXhELFdBQVU7MENBQ2IsNEVBQUNDO29DQUNDQyxlQUFlMkMsa0JBQWtCSyxRQUFRO29DQUN6Q3hGLE9BQU9zRSxXQUFXdEUsS0FBSyxHQUFHO29DQUMxQkMsUUFBUTtvQ0FDUndDLGFBQWFpQixPQUFPakIsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBT3RDaUIsT0FBT0ksWUFBWSxJQUFJTixnQkFBZ0JqQyxNQUFNLEdBQUcsbUJBQy9DLDhEQUFDbkMsaURBQU1BLENBQUMwRyxHQUFHO3dCQUNUUSxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHM0UsR0FBRzt3QkFBRzt3QkFDN0JNLFNBQVM7NEJBQUVxRSxTQUFTOzRCQUFHM0UsR0FBRzt3QkFBRTt3QkFDNUJVLFdBQVU7OzBDQUVWLDhEQUFDd0Q7Z0NBQUl4RCxXQUFVOztrREFDYiw4REFBQzdDLHFKQUFVQTt3Q0FBQzZDLFdBQVU7Ozs7OztrREFDdEIsOERBQUM0RDt3Q0FBSzVELFdBQVU7a0RBQW9DOzs7Ozs7Ozs7Ozs7MENBRXRELDhEQUFDd0Q7Z0NBQUl4RCxXQUFVOzBDQUNaa0IsZ0JBQWdCOEIsR0FBRyxDQUFDLENBQUNxQyxRQUFRMUUsc0JBQzVCLDhEQUFDNkM7d0NBQWdCeEQsV0FBVTs7MERBQ3pCLDhEQUFDd0Q7Z0RBQUl4RCxXQUFVOztrRUFDYiw4REFBQzREO3dEQUFLNUQsV0FBVTtrRUFDYnFGLE9BQU9iLElBQUksSUFBSTs7Ozs7O2tFQUVsQiw4REFBQ1o7d0RBQUs1RCxXQUFVOzs0REFDYk8sS0FBSzZFLEtBQUssQ0FBQyxDQUFDQyxPQUFPQyxVQUFVLElBQUksS0FBSzs0REFBSzs7Ozs7Ozs7Ozs7OzswREFHaEQsOERBQUM5QjtnREFBSXhELFdBQVU7MERBQ2IsNEVBQUN3RDtvREFDQ3hELFdBQVU7b0RBQ1Z1RixPQUFPO3dEQUFFN0gsT0FBTyxHQUFrQyxPQUEvQixDQUFDMkgsT0FBT0MsVUFBVSxJQUFJLEtBQUssS0FBSTtvREFBRzs7Ozs7Ozs7Ozs7O3VDQVpqRDNFOzs7Ozs7Ozs7Ozs7Ozs7O29CQXNCakIsQ0FBQ1EsZUFBZUQsZ0JBQWdCakMsTUFBTSxLQUFLLG1CQUMxQyw4REFBQ3VFO3dCQUFJeEQsV0FBVTs7MENBQ2IsOERBQUMvQyxvSkFBUUE7Z0NBQUMrQyxXQUFVOzs7Ozs7MENBQ3BCLDhEQUFDd0Y7Z0NBQUd4RixXQUFVOzBDQUF5Qzs7Ozs7OzBDQUd2RCw4REFBQzBEO2dDQUFFMUQsV0FBVTswQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFqRCxFQUFFO0lBM1FXZ0I7TUFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvZXhwbG9yZXIvUGF0dGVyblZpc3VhbGl6YXRpb24udHN4PzM4NTIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlUmVmLCB1c2VTdGF0ZSwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBcbiAgQmFyQ2hhcnQzLCBcbiAgQWN0aXZpdHksIFxuICBXYXZlcywgXG4gIFRyZW5kaW5nVXAsXG4gIEV5ZSxcbiAgRXllT2ZmLFxuICBNYXhpbWl6ZTIsXG4gIE1pbmltaXplMixcbiAgU2V0dGluZ3MsXG4gIExvYWRlcjJcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcblxuaW1wb3J0IHsgTmF0dXJhbFNvdW5kUGF0dGVybiB9IGZyb20gJ0AvdHlwZXMvTmF0dXJhbFBhdHRlcm4udHlwZXMnO1xuXG5pbnRlcmZhY2UgUGF0dGVyblZpc3VhbGl6YXRpb25Qcm9wcyB7XG4gIHBhdHRlcm46IE5hdHVyYWxTb3VuZFBhdHRlcm47XG4gIGFuYWx5c2lzUmVzdWx0cz86IGFueVtdO1xuICBpc0FuYWx5emluZz86IGJvb2xlYW47XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIFZpc3VhbGl6YXRpb25Db25maWcge1xuICBzaG93V2F2ZWZvcm06IGJvb2xlYW47XG4gIHNob3dTcGVjdHJ1bTogYm9vbGVhbjtcbiAgc2hvd1BhdHRlcm5zOiBib29sZWFuO1xuICBzaG93SGFybW9uaWNzOiBib29sZWFuO1xuICBjb2xvclNjaGVtZTogJ2Nvc21pYycgfCAnbmF0dXJhbCcgfCAnbW9ub2Nocm9tZSc7XG4gIHNlbnNpdGl2aXR5OiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBXYXZlZm9ybUNhbnZhc1Byb3BzIHtcbiAgd2F2ZWZvcm1EYXRhOiBGbG9hdDMyQXJyYXk7XG4gIHdpZHRoOiBudW1iZXI7XG4gIGhlaWdodDogbnVtYmVyO1xuICBjb2xvcjogc3RyaW5nO1xuICBpc0FuaW1hdGVkPzogYm9vbGVhbjtcbn1cblxuY29uc3QgV2F2ZWZvcm1DYW52YXM6IFJlYWN0LkZDPFdhdmVmb3JtQ2FudmFzUHJvcHM+ID0gKHtcbiAgd2F2ZWZvcm1EYXRhLFxuICB3aWR0aCxcbiAgaGVpZ2h0LFxuICBjb2xvcixcbiAgaXNBbmltYXRlZCA9IGZhbHNlLFxufSkgPT4ge1xuICBjb25zdCBjYW52YXNSZWYgPSB1c2VSZWY8SFRNTENhbnZhc0VsZW1lbnQ+KG51bGwpO1xuICBjb25zdCBhbmltYXRpb25SZWYgPSB1c2VSZWY8bnVtYmVyPigpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgY2FudmFzID0gY2FudmFzUmVmLmN1cnJlbnQ7XG4gICAgaWYgKCFjYW52YXMpIHJldHVybjtcblxuICAgIGNvbnN0IGN0eCA9IGNhbnZhcy5nZXRDb250ZXh0KCcyZCcpO1xuICAgIGlmICghY3R4KSByZXR1cm47XG5cbiAgICBjb25zdCBkcmF3V2F2ZWZvcm0gPSAob2Zmc2V0ID0gMCkgPT4ge1xuICAgICAgY3R4LmNsZWFyUmVjdCgwLCAwLCB3aWR0aCwgaGVpZ2h0KTtcbiAgICAgIFxuICAgICAgLy8gU2V0IHVwIGRyYXdpbmcgc3R5bGVcbiAgICAgIGN0eC5zdHJva2VTdHlsZSA9IGNvbG9yO1xuICAgICAgY3R4LmxpbmVXaWR0aCA9IDI7XG4gICAgICBjdHgubGluZUNhcCA9ICdyb3VuZCc7XG4gICAgICBjdHgubGluZUpvaW4gPSAncm91bmQnO1xuXG4gICAgICAvLyBDcmVhdGUgZ3JhZGllbnQgZm9yIHZpc3VhbCBhcHBlYWxcbiAgICAgIGNvbnN0IGdyYWRpZW50ID0gY3R4LmNyZWF0ZUxpbmVhckdyYWRpZW50KDAsIDAsIDAsIGhlaWdodCk7XG4gICAgICBncmFkaWVudC5hZGRDb2xvclN0b3AoMCwgY29sb3IpO1xuICAgICAgZ3JhZGllbnQuYWRkQ29sb3JTdG9wKDEsIGNvbG9yICsgJzQwJyk7XG4gICAgICBjdHguc3Ryb2tlU3R5bGUgPSBncmFkaWVudDtcblxuICAgICAgLy8gRHJhdyB3YXZlZm9ybVxuICAgICAgY3R4LmJlZ2luUGF0aCgpO1xuICAgICAgY29uc3Qgc2xpY2VXaWR0aCA9IHdpZHRoIC8gd2F2ZWZvcm1EYXRhLmxlbmd0aDtcbiAgICAgIGxldCB4ID0gMDtcblxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB3YXZlZm9ybURhdGEubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgZGF0YUluZGV4ID0gaXNBbmltYXRlZCA/IChpICsgb2Zmc2V0KSAlIHdhdmVmb3JtRGF0YS5sZW5ndGggOiBpO1xuICAgICAgICBjb25zdCB2ID0gd2F2ZWZvcm1EYXRhW2RhdGFJbmRleF0gKiAwLjU7XG4gICAgICAgIGNvbnN0IHkgPSAodiAqIGhlaWdodCAvIDIpICsgKGhlaWdodCAvIDIpO1xuXG4gICAgICAgIGlmIChpID09PSAwKSB7XG4gICAgICAgICAgY3R4Lm1vdmVUbyh4LCB5KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjdHgubGluZVRvKHgsIHkpO1xuICAgICAgICB9XG5cbiAgICAgICAgeCArPSBzbGljZVdpZHRoO1xuICAgICAgfVxuXG4gICAgICBjdHguc3Ryb2tlKCk7XG5cbiAgICAgIC8vIEFkZCBnbG93IGVmZmVjdFxuICAgICAgY3R4LnNoYWRvd0NvbG9yID0gY29sb3I7XG4gICAgICBjdHguc2hhZG93Qmx1ciA9IDEwO1xuICAgICAgY3R4LnN0cm9rZSgpO1xuICAgICAgY3R4LnNoYWRvd0JsdXIgPSAwO1xuICAgIH07XG5cbiAgICBpZiAoaXNBbmltYXRlZCkge1xuICAgICAgbGV0IG9mZnNldCA9IDA7XG4gICAgICBjb25zdCBhbmltYXRlID0gKCkgPT4ge1xuICAgICAgICBkcmF3V2F2ZWZvcm0ob2Zmc2V0KTtcbiAgICAgICAgb2Zmc2V0ICs9IDI7XG4gICAgICAgIGFuaW1hdGlvblJlZi5jdXJyZW50ID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKGFuaW1hdGUpO1xuICAgICAgfTtcbiAgICAgIGFuaW1hdGUoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgZHJhd1dhdmVmb3JtKCk7XG4gICAgfVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChhbmltYXRpb25SZWYuY3VycmVudCkge1xuICAgICAgICBjYW5jZWxBbmltYXRpb25GcmFtZShhbmltYXRpb25SZWYuY3VycmVudCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW3dhdmVmb3JtRGF0YSwgd2lkdGgsIGhlaWdodCwgY29sb3IsIGlzQW5pbWF0ZWRdKTtcblxuICByZXR1cm4gKFxuICAgIDxjYW52YXNcbiAgICAgIHJlZj17Y2FudmFzUmVmfVxuICAgICAgd2lkdGg9e3dpZHRofVxuICAgICAgaGVpZ2h0PXtoZWlnaHR9XG4gICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWxnXCJcbiAgICAvPlxuICApO1xufTtcblxuaW50ZXJmYWNlIFNwZWN0cnVtQW5hbHl6ZXJQcm9wcyB7XG4gIGZyZXF1ZW5jeURhdGE6IHtcbiAgICBmcmVxdWVuY3lCaW5zOiBudW1iZXJbXTtcbiAgICBtYWduaXR1ZGVzOiBudW1iZXJbXTtcbiAgfTtcbiAgd2lkdGg6IG51bWJlcjtcbiAgaGVpZ2h0OiBudW1iZXI7XG4gIGNvbG9yU2NoZW1lOiBzdHJpbmc7XG59XG5cbmNvbnN0IFNwZWN0cnVtQW5hbHl6ZXI6IFJlYWN0LkZDPFNwZWN0cnVtQW5hbHl6ZXJQcm9wcz4gPSAoe1xuICBmcmVxdWVuY3lEYXRhLFxuICB3aWR0aCxcbiAgaGVpZ2h0LFxuICBjb2xvclNjaGVtZSxcbn0pID0+IHtcbiAgY29uc3QgY2FudmFzUmVmID0gdXNlUmVmPEhUTUxDYW52YXNFbGVtZW50PihudWxsKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNhbnZhcyA9IGNhbnZhc1JlZi5jdXJyZW50O1xuICAgIGlmICghY2FudmFzKSByZXR1cm47XG5cbiAgICBjb25zdCBjdHggPSBjYW52YXMuZ2V0Q29udGV4dCgnMmQnKTtcbiAgICBpZiAoIWN0eCkgcmV0dXJuO1xuXG4gICAgY3R4LmNsZWFyUmVjdCgwLCAwLCB3aWR0aCwgaGVpZ2h0KTtcblxuICAgIGNvbnN0IGJhcldpZHRoID0gd2lkdGggLyBmcmVxdWVuY3lEYXRhLm1hZ25pdHVkZXMubGVuZ3RoO1xuICAgIGNvbnN0IG1heE1hZ25pdHVkZSA9IE1hdGgubWF4KC4uLmZyZXF1ZW5jeURhdGEubWFnbml0dWRlcyk7XG5cbiAgICBmcmVxdWVuY3lEYXRhLm1hZ25pdHVkZXMuZm9yRWFjaCgobWFnbml0dWRlLCBpbmRleCkgPT4ge1xuICAgICAgY29uc3QgYmFySGVpZ2h0ID0gKG1hZ25pdHVkZSAvIG1heE1hZ25pdHVkZSkgKiBoZWlnaHQ7XG4gICAgICBjb25zdCB4ID0gaW5kZXggKiBiYXJXaWR0aDtcbiAgICAgIGNvbnN0IHkgPSBoZWlnaHQgLSBiYXJIZWlnaHQ7XG5cbiAgICAgIC8vIENyZWF0ZSBncmFkaWVudCBiYXNlZCBvbiBmcmVxdWVuY3lcbiAgICAgIGNvbnN0IGh1ZSA9IChpbmRleCAvIGZyZXF1ZW5jeURhdGEubWFnbml0dWRlcy5sZW5ndGgpICogMjQwOyAvLyBCbHVlIHRvIHJlZFxuICAgICAgY29uc3QgZ3JhZGllbnQgPSBjdHguY3JlYXRlTGluZWFyR3JhZGllbnQoMCwgeSwgMCwgaGVpZ2h0KTtcbiAgICAgIFxuICAgICAgaWYgKGNvbG9yU2NoZW1lID09PSAnY29zbWljJykge1xuICAgICAgICBncmFkaWVudC5hZGRDb2xvclN0b3AoMCwgYGhzbCgkezI0MCArIGh1ZSAqIDAuNX0sIDcwJSwgNjAlKWApO1xuICAgICAgICBncmFkaWVudC5hZGRDb2xvclN0b3AoMSwgYGhzbCgkezI0MCArIGh1ZSAqIDAuNX0sIDcwJSwgMzAlKWApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZ3JhZGllbnQuYWRkQ29sb3JTdG9wKDAsIGBoc2woJHtodWV9LCA3MCUsIDYwJSlgKTtcbiAgICAgICAgZ3JhZGllbnQuYWRkQ29sb3JTdG9wKDEsIGBoc2woJHtodWV9LCA3MCUsIDMwJSlgKTtcbiAgICAgIH1cblxuICAgICAgY3R4LmZpbGxTdHlsZSA9IGdyYWRpZW50O1xuICAgICAgY3R4LmZpbGxSZWN0KHgsIHksIGJhcldpZHRoIC0gMSwgYmFySGVpZ2h0KTtcbiAgICB9KTtcbiAgfSwgW2ZyZXF1ZW5jeURhdGEsIHdpZHRoLCBoZWlnaHQsIGNvbG9yU2NoZW1lXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8Y2FudmFzXG4gICAgICByZWY9e2NhbnZhc1JlZn1cbiAgICAgIHdpZHRoPXt3aWR0aH1cbiAgICAgIGhlaWdodD17aGVpZ2h0fVxuICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1sZ1wiXG4gICAgLz5cbiAgKTtcbn07XG5cbmV4cG9ydCBjb25zdCBQYXR0ZXJuVmlzdWFsaXphdGlvbjogUmVhY3QuRkM8UGF0dGVyblZpc3VhbGl6YXRpb25Qcm9wcz4gPSAoe1xuICBwYXR0ZXJuLFxuICBhbmFseXNpc1Jlc3VsdHMgPSBbXSxcbiAgaXNBbmFseXppbmcgPSBmYWxzZSxcbiAgY2xhc3NOYW1lID0gJycsXG59KSA9PiB7XG4gIGNvbnN0IFtjb25maWcsIHNldENvbmZpZ10gPSB1c2VTdGF0ZTxWaXN1YWxpemF0aW9uQ29uZmlnPih7XG4gICAgc2hvd1dhdmVmb3JtOiB0cnVlLFxuICAgIHNob3dTcGVjdHJ1bTogdHJ1ZSxcbiAgICBzaG93UGF0dGVybnM6IHRydWUsXG4gICAgc2hvd0hhcm1vbmljczogZmFsc2UsXG4gICAgY29sb3JTY2hlbWU6ICdjb3NtaWMnLFxuICAgIHNlbnNpdGl2aXR5OiAwLjcsXG4gIH0pO1xuICBjb25zdCBbaXNFeHBhbmRlZCwgc2V0SXNFeHBhbmRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93U2V0dGluZ3MsIHNldFNob3dTZXR0aW5nc10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgY29udGFpbmVyUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcbiAgY29uc3QgW2RpbWVuc2lvbnMsIHNldERpbWVuc2lvbnNdID0gdXNlU3RhdGUoeyB3aWR0aDogODAwLCBoZWlnaHQ6IDIwMCB9KTtcblxuICAvLyBVcGRhdGUgZGltZW5zaW9ucyBvbiByZXNpemVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB1cGRhdGVEaW1lbnNpb25zID0gKCkgPT4ge1xuICAgICAgaWYgKGNvbnRhaW5lclJlZi5jdXJyZW50KSB7XG4gICAgICAgIGNvbnN0IHJlY3QgPSBjb250YWluZXJSZWYuY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICAgICAgc2V0RGltZW5zaW9ucyh7XG4gICAgICAgICAgd2lkdGg6IHJlY3Qud2lkdGggLSAzMiwgLy8gQWNjb3VudCBmb3IgcGFkZGluZ1xuICAgICAgICAgIGhlaWdodDogaXNFeHBhbmRlZCA/IDQwMCA6IDIwMCxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIHVwZGF0ZURpbWVuc2lvbnMoKTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdXBkYXRlRGltZW5zaW9ucyk7XG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB1cGRhdGVEaW1lbnNpb25zKTtcbiAgfSwgW2lzRXhwYW5kZWRdKTtcblxuICAvLyBDb2xvciBzY2hlbWUgbWFwcGluZ1xuICBjb25zdCBjb2xvclNjaGVtZXMgPSB7XG4gICAgY29zbWljOiAnIzhCNUNGNicsXG4gICAgbmF0dXJhbDogJyMxMEI5ODEnLFxuICAgIG1vbm9jaHJvbWU6ICcjNkI3MjgwJyxcbiAgfTtcblxuICBjb25zdCBwcmltYXJ5Q29sb3IgPSBjb2xvclNjaGVtZXNbY29uZmlnLmNvbG9yU2NoZW1lXTtcblxuICAvLyBQcmVwYXJlIHZpc3VhbGl6YXRpb24gZGF0YVxuICBjb25zdCB2aXN1YWxpemF0aW9uRGF0YSA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIHJldHVybiB7XG4gICAgICB3YXZlZm9ybTogcGF0dGVybi53YXZlZm9ybURhdGEgfHwgbmV3IEZsb2F0MzJBcnJheSgxMDI0KS5tYXAoKCkgPT4gTWF0aC5yYW5kb20oKSAqIDIgLSAxKSxcbiAgICAgIHNwZWN0cnVtOiB7XG4gICAgICAgIGZyZXF1ZW5jeUJpbnM6IHBhdHRlcm4uZnJlcXVlbmN5RGF0YT8uZnJlcXVlbmN5QmlucyB8fCBbXSxcbiAgICAgICAgbWFnbml0dWRlczogcGF0dGVybi5mcmVxdWVuY3lEYXRhPy5tYWduaXR1ZGVzIHx8IFtdLFxuICAgICAgfSxcbiAgICAgIGhhcm1vbmljczogcGF0dGVybi5tZXRhZGF0YS5oYXJtb25pY1NlcmllcyB8fCBbXSxcbiAgICAgIHBhdHRlcm5zOiBhbmFseXNpc1Jlc3VsdHMsXG4gICAgfTtcbiAgfSwgW3BhdHRlcm4sIGFuYWx5c2lzUmVzdWx0c10pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BnbGFzcyAke2NsYXNzTmFtZX1gfSByZWY9e2NvbnRhaW5lclJlZn0+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlci1iIGJvcmRlci13aGl0ZS8xMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGJnLWNvc21pYy01MDAvMjAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgPEFjdGl2aXR5IGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1jb3NtaWMtNDAwXCIgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgIFBhdHRlcm4gVmlzdWFsaXphdGlvblxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNjAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICBSZWFsLXRpbWUgYW5hbHlzaXMgb2Yge3BhdHRlcm4ubmFtZX1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICB7aXNBbmFseXppbmcgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC1jb3NtaWMtNDAwXCI+XG4gICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cInctNCBoLTQgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkFuYWx5emluZy4uLjwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93U2V0dGluZ3MoIXNob3dTZXR0aW5ncyl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC13aGl0ZS83MCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgIHRpdGxlPVwiVmlzdWFsaXphdGlvbiBzZXR0aW5nc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNFeHBhbmRlZCghaXNFeHBhbmRlZCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC13aGl0ZS83MCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgIHRpdGxlPXtpc0V4cGFuZGVkID8gJ01pbmltaXplJyA6ICdFeHBhbmQnfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtpc0V4cGFuZGVkID8gKFxuICAgICAgICAgICAgICA8TWluaW1pemUyIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPE1heGltaXplMiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBTZXR0aW5ncyBQYW5lbCAqL31cbiAgICAgIDxBbmltYXRlUHJlc2VuY2U+XG4gICAgICAgIHtzaG93U2V0dGluZ3MgJiYgKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIGhlaWdodDogMCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBoZWlnaHQ6ICdhdXRvJyB9fVxuICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCBoZWlnaHQ6IDAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci13aGl0ZS8xMCBwLTQgYmctd2hpdGUvNVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgICAgIHsvKiBWaXN1YWxpemF0aW9uIHRvZ2dsZXMgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZS84MFwiPkRpc3BsYXk8L2xhYmVsPlxuICAgICAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgICAgICB7IGtleTogJ3Nob3dXYXZlZm9ybScsIGxhYmVsOiAnV2F2ZWZvcm0nLCBpY29uOiBXYXZlcyB9LFxuICAgICAgICAgICAgICAgICAgeyBrZXk6ICdzaG93U3BlY3RydW0nLCBsYWJlbDogJ1NwZWN0cnVtJywgaWNvbjogQmFyQ2hhcnQzIH0sXG4gICAgICAgICAgICAgICAgICB7IGtleTogJ3Nob3dQYXR0ZXJucycsIGxhYmVsOiAnUGF0dGVybnMnLCBpY29uOiBUcmVuZGluZ1VwIH0sXG4gICAgICAgICAgICAgICAgXS5tYXAoKHsga2V5LCBsYWJlbCwgaWNvbjogSWNvbiB9KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8bGFiZWwga2V5PXtrZXl9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvbmZpZ1trZXkgYXMga2V5b2YgVmlzdWFsaXphdGlvbkNvbmZpZ10gYXMgYm9vbGVhbn1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvbmZpZyhwcmV2ID0+ICh7IC4uLnByZXYsIFtrZXldOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkIGJvcmRlci13aGl0ZS8yMCBiZy13aGl0ZS8xMCB0ZXh0LWNvc21pYy01MDAgZm9jdXM6cmluZy1jb3NtaWMtNDAwXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPEljb24gY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LXdoaXRlLzYwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXdoaXRlLzgwXCI+e2xhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBDb2xvciBzY2hlbWUgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZS84MFwiPkNvbG9yIFNjaGVtZTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5jb2xvclNjaGVtZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29uZmlnKHByZXYgPT4gKHsgLi4ucHJldiwgY29sb3JTY2hlbWU6IGUudGFyZ2V0LnZhbHVlIGFzIGFueSB9KSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUvMTAgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByb3VuZGVkLWxnIHB4LTMgcHktMSB0ZXh0LXNtIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjb3NtaWNcIj5Db3NtaWM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJuYXR1cmFsXCI+TmF0dXJhbDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm1vbm9jaHJvbWVcIj5Nb25vY2hyb21lPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBTZW5zaXRpdml0eSAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlLzgwXCI+U2Vuc2l0aXZpdHk8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInJhbmdlXCJcbiAgICAgICAgICAgICAgICAgIG1pbj1cIjAuMVwiXG4gICAgICAgICAgICAgICAgICBtYXg9XCIxXCJcbiAgICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjFcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5zZW5zaXRpdml0eX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29uZmlnKHByZXYgPT4gKHsgLi4ucHJldiwgc2Vuc2l0aXZpdHk6IHBhcnNlRmxvYXQoZS50YXJnZXQudmFsdWUpIH0pKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtd2hpdGUvNjBcIj57TWF0aC5yb3VuZChjb25maWcuc2Vuc2l0aXZpdHkgKiAxMDApfSU8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICApfVxuICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XG5cbiAgICAgIHsvKiBWaXN1YWxpemF0aW9uIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBzcGFjZS15LTRcIj5cbiAgICAgICAgey8qIFdhdmVmb3JtICovfVxuICAgICAgICB7Y29uZmlnLnNob3dXYXZlZm9ybSAmJiAoXG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS0yXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8V2F2ZXMgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlLzYwXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlLzgwXCI+V2F2ZWZvcm08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmxhY2svMjAgcm91bmRlZC1sZyBwLTJcIj5cbiAgICAgICAgICAgICAgPFdhdmVmb3JtQ2FudmFzXG4gICAgICAgICAgICAgICAgd2F2ZWZvcm1EYXRhPXt2aXN1YWxpemF0aW9uRGF0YS53YXZlZm9ybX1cbiAgICAgICAgICAgICAgICB3aWR0aD17ZGltZW5zaW9ucy53aWR0aCAtIDE2fVxuICAgICAgICAgICAgICAgIGhlaWdodD17MTAwfVxuICAgICAgICAgICAgICAgIGNvbG9yPXtwcmltYXJ5Q29sb3J9XG4gICAgICAgICAgICAgICAgaXNBbmltYXRlZD17aXNBbmFseXppbmd9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFNwZWN0cnVtICovfVxuICAgICAgICB7Y29uZmlnLnNob3dTcGVjdHJ1bSAmJiB2aXN1YWxpemF0aW9uRGF0YS5zcGVjdHJ1bS5tYWduaXR1ZGVzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktMlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPEJhckNoYXJ0MyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGUvNjBcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUvODBcIj5GcmVxdWVuY3kgU3BlY3RydW08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmxhY2svMjAgcm91bmRlZC1sZyBwLTJcIj5cbiAgICAgICAgICAgICAgPFNwZWN0cnVtQW5hbHl6ZXJcbiAgICAgICAgICAgICAgICBmcmVxdWVuY3lEYXRhPXt2aXN1YWxpemF0aW9uRGF0YS5zcGVjdHJ1bX1cbiAgICAgICAgICAgICAgICB3aWR0aD17ZGltZW5zaW9ucy53aWR0aCAtIDE2fVxuICAgICAgICAgICAgICAgIGhlaWdodD17MTIwfVxuICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPXtjb25maWcuY29sb3JTY2hlbWV9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFBhdHRlcm4gQW5hbHlzaXMgUmVzdWx0cyAqL31cbiAgICAgICAge2NvbmZpZy5zaG93UGF0dGVybnMgJiYgYW5hbHlzaXNSZXN1bHRzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktMlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlLzYwXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlLzgwXCI+RGV0ZWN0ZWQgUGF0dGVybnM8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtM1wiPlxuICAgICAgICAgICAgICB7YW5hbHlzaXNSZXN1bHRzLm1hcCgocmVzdWx0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiYmctd2hpdGUvNSByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7cmVzdWx0LnR5cGUgfHwgJ1BhdHRlcm4nfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1jb3NtaWMtNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge01hdGgucm91bmQoKHJlc3VsdC5jb25maWRlbmNlIHx8IDApICogMTAwKX0lXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUvMTAgcm91bmRlZC1mdWxsIGgtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWNvc21pYy00MDAgdG8tY29zbWljLTUwMCBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAkeyhyZXN1bHQuY29uZmlkZW5jZSB8fCAwKSAqIDEwMH0lYCB9fVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBFbXB0eSBzdGF0ZSAqL31cbiAgICAgICAgeyFpc0FuYWx5emluZyAmJiBhbmFseXNpc1Jlc3VsdHMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgIDxBY3Rpdml0eSBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgbXgtYXV0byBtYi00IHRleHQtd2hpdGUvNDBcIiAvPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC13aGl0ZS82MCBtYi0yXCI+XG4gICAgICAgICAgICAgIE5vIEFuYWx5c2lzIERhdGFcbiAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzQwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgUGF0dGVybiBhbmFseXNpcyB3aWxsIGFwcGVhciBoZXJlIHdoZW4gYXZhaWxhYmxlXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsInVzZU1lbW8iLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJCYXJDaGFydDMiLCJBY3Rpdml0eSIsIldhdmVzIiwiVHJlbmRpbmdVcCIsIk1heGltaXplMiIsIk1pbmltaXplMiIsIlNldHRpbmdzIiwiTG9hZGVyMiIsIldhdmVmb3JtQ2FudmFzIiwid2F2ZWZvcm1EYXRhIiwid2lkdGgiLCJoZWlnaHQiLCJjb2xvciIsImlzQW5pbWF0ZWQiLCJjYW52YXNSZWYiLCJhbmltYXRpb25SZWYiLCJ1c2VFZmZlY3QiLCJjYW52YXMiLCJjdXJyZW50IiwiY3R4IiwiZ2V0Q29udGV4dCIsImRyYXdXYXZlZm9ybSIsIm9mZnNldCIsImNsZWFyUmVjdCIsInN0cm9rZVN0eWxlIiwibGluZVdpZHRoIiwibGluZUNhcCIsImxpbmVKb2luIiwiZ3JhZGllbnQiLCJjcmVhdGVMaW5lYXJHcmFkaWVudCIsImFkZENvbG9yU3RvcCIsImJlZ2luUGF0aCIsInNsaWNlV2lkdGgiLCJsZW5ndGgiLCJ4IiwiaSIsImRhdGFJbmRleCIsInYiLCJ5IiwibW92ZVRvIiwibGluZVRvIiwic3Ryb2tlIiwic2hhZG93Q29sb3IiLCJzaGFkb3dCbHVyIiwiYW5pbWF0ZSIsInJlcXVlc3RBbmltYXRpb25GcmFtZSIsImNhbmNlbEFuaW1hdGlvbkZyYW1lIiwicmVmIiwiY2xhc3NOYW1lIiwiU3BlY3RydW1BbmFseXplciIsImZyZXF1ZW5jeURhdGEiLCJjb2xvclNjaGVtZSIsImJhcldpZHRoIiwibWFnbml0dWRlcyIsIm1heE1hZ25pdHVkZSIsIk1hdGgiLCJtYXgiLCJmb3JFYWNoIiwibWFnbml0dWRlIiwiaW5kZXgiLCJiYXJIZWlnaHQiLCJodWUiLCJmaWxsU3R5bGUiLCJmaWxsUmVjdCIsIlBhdHRlcm5WaXN1YWxpemF0aW9uIiwicGF0dGVybiIsImFuYWx5c2lzUmVzdWx0cyIsImlzQW5hbHl6aW5nIiwiY29uZmlnIiwic2V0Q29uZmlnIiwic2hvd1dhdmVmb3JtIiwic2hvd1NwZWN0cnVtIiwic2hvd1BhdHRlcm5zIiwic2hvd0hhcm1vbmljcyIsInNlbnNpdGl2aXR5IiwiaXNFeHBhbmRlZCIsInNldElzRXhwYW5kZWQiLCJzaG93U2V0dGluZ3MiLCJzZXRTaG93U2V0dGluZ3MiLCJjb250YWluZXJSZWYiLCJkaW1lbnNpb25zIiwic2V0RGltZW5zaW9ucyIsInVwZGF0ZURpbWVuc2lvbnMiLCJyZWN0IiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0Iiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJjb2xvclNjaGVtZXMiLCJjb3NtaWMiLCJuYXR1cmFsIiwibW9ub2Nocm9tZSIsInByaW1hcnlDb2xvciIsInZpc3VhbGl6YXRpb25EYXRhIiwid2F2ZWZvcm0iLCJGbG9hdDMyQXJyYXkiLCJtYXAiLCJyYW5kb20iLCJzcGVjdHJ1bSIsImZyZXF1ZW5jeUJpbnMiLCJoYXJtb25pY3MiLCJtZXRhZGF0YSIsImhhcm1vbmljU2VyaWVzIiwicGF0dGVybnMiLCJkaXYiLCJoMyIsInAiLCJuYW1lIiwic3BhbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ0aXRsZSIsImluaXRpYWwiLCJvcGFjaXR5IiwiZXhpdCIsImxhYmVsIiwia2V5IiwiaWNvbiIsIkljb24iLCJpbnB1dCIsInR5cGUiLCJjaGVja2VkIiwib25DaGFuZ2UiLCJlIiwicHJldiIsInRhcmdldCIsInNlbGVjdCIsInZhbHVlIiwib3B0aW9uIiwibWluIiwic3RlcCIsInBhcnNlRmxvYXQiLCJyb3VuZCIsInJlc3VsdCIsImNvbmZpZGVuY2UiLCJzdHlsZSIsImg0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/explorer/PatternVisualization.tsx\n"));

/***/ })

});