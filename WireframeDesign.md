# Cosmic Mobile App - Wireframe Specification & Design Brief

## Executive Summary

The Cosmic mobile app is an innovative musical platform that bridges Indian classical music, microtonal systems, and natural pattern recognition. This specification outlines the mobile-first design approach for a complex application that combines advanced audio synthesis, educational content, and interactive visualizations.

**Key Design Principles:**
- Mobile-first responsive design
- Touch-friendly interaction patterns
- Accessibility compliance (WCAG 2.1 AA)
- Performance-optimized for real-time audio
- Cultural authenticity in musical representation

---

## App Overview

### Core Value Proposition
Enable users to explore the mathematical relationships between nature's patterns, Indian classical music, and universal harmonic principles through interactive audio-visual experiences.

### Target Users
- Music students and educators
- Indian classical music enthusiasts
- Sound designers and composers
- Mathematics and nature pattern enthusiasts
- Cross-cultural music researchers

### Platform Requirements
- Progressive Web App (PWA) for cross-platform compatibility
- Offline-capable for core features
- Real-time audio processing capabilities
- <2 second pattern correlation loading
- <100MB memory usage during operation

---

## Information Architecture

```
Cosmic App
├── Home/Dashboard
├── Audio Engine
│   ├── Shruti Explorer
│   ├── Raga Laboratory
│   └── Tala Cycles
├── Pattern Explorer
│   ├── Natural Sounds Library
│   ├── Pattern Correlations
│   └── Educational Context
├── Visualizations
│   ├── Waveforms
│   ├── Frequency Spectrum
│   └── Pattern Relationships
└── Settings/Profile
    ├── Audio Preferences
    ├── Accessibility Options
    └── Learning Progress
```

---

## Screen-by-Screen Wireframes

### 1. Home/Dashboard Screen

**Primary Elements:**
- **Header**: App logo, theme toggle, settings icon
- **Hero Section**: Current session summary, daily pattern discovery
- **Quick Actions**: Play last session, explore new pattern, practice mode
- **Featured Content**: Pattern of the day, raga spotlight, nature sound highlight
- **Navigation**: Bottom tab bar with 4 primary sections

**Layout Structure:**
```
┌─────────────────────────────────────┐
│ [☰] Cosmic           [🌙] [⚙️]     │
├─────────────────────────────────────┤
│ Welcome back, Explorer              │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ Today's Discovery               │ │
│ │ Ocean Waves ↔ Raga Yaman       │ │
│ │ [▶️ Listen] [📊 Analyze]        │ │
│ └─────────────────────────────────┘ │
│                                     │
│ Quick Actions:                      │
│ [🎵 Audio Lab] [🌿 Nature Explorer] │
│ [📈 Patterns] [🎭 Raga Practice]   │
│                                     │
│ Featured Learning:                  │
│ • Golden Ratio in Shell Spirals    │
│ • Fibonacci in Tala Cycles         │
│ • Harmonic Series in Bird Songs    │
├─────────────────────────────────────┤
│ [🏠] [🎵] [🌿] [📊] [👤]          │
└─────────────────────────────────────┘
```

**Interaction Patterns:**
- Swipe gestures for featured content carousel
- Long-press for quick actions context menu
- Pull-to-refresh for new daily content

### 2. Audio Engine - Shruti Explorer

**Primary Elements:**
- **Shruti Wheel**: 22-position circular interface
- **Frequency Display**: Real-time frequency readout
- **Base Frequency Slider**: 220Hz - 880Hz adjustment
- **Playback Controls**: Play/pause/stop with visual feedback
- **Precision Indicator**: ±0.1 cents accuracy display

**Layout Structure:**
```
┌─────────────────────────────────────┐
│ ← Shruti Explorer          [🎛️]    │
├─────────────────────────────────────┤
│        ┌─────────────────────┐      │
│        │        Sa          │      │
│        │    ●           ●    │      │
│        │ Re              Ni  │      │
│        │●     ┌─────┐     ●│      │
│        │   Ga│ 440Hz│  Dha   │      │
│        │  ●  │±0.1¢ │  ●    │      │
│        │     └─────┘        │      │
│        │ Ma  ●     ●    Pa  │      │
│        │        ●           │      │
│        └─────────────────────┘      │
│                                     │
│ Base Frequency: [────●────] 440Hz   │
│                                     │
│ Current: Komal Gandhar              │
│ Frequency: 297.33 Hz                │
│                                     │
│ [▶️] [⏸️] [⏹️]     [🎵 Meend]      │
├─────────────────────────────────────┤
│ [🏠] [🎵] [🌿] [📊] [👤]          │
└─────────────────────────────────────┘
```

**Interaction Patterns:**
- Tap shruti positions for immediate playback
- Drag for smooth microtonal transitions (meend)
- Pinch-to-zoom for precise frequency adjustment
- Haptic feedback for shruti selection

### 3. Raga Laboratory

**Primary Elements:**
- **Raga Selector**: Searchable dropdown with 10 major ragas
- **Aroha/Avaroha Display**: Ascending/descending pattern visualization
- **Vadi/Samvadi Indicators**: Primary/secondary note highlighting
- **Pakad Playback**: Characteristic phrase demonstration
- **Time Association**: Raga performance time suggestions

**Layout Structure:**
```
┌─────────────────────────────────────┐
│ ← Raga Laboratory          [🔍]     │
├─────────────────────────────────────┤
│ Selected: Raga Yaman ⌄             │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ Aroha: Sa Re Ga Ma# Dha Ni Sa' │ │
│ │ Avaroha: Sa' Ni Dha Ma# Ga Re Sa│ │
│ │                                 │ │
│ │ Vadi: Ga (Primary)              │ │
│ │ Samvadi: Ni (Secondary)         │ │
│ │                                 │ │
│ │ Time: Evening (7-10 PM)         │ │
│ └─────────────────────────────────┘ │
│                                     │
│ Pakad (Characteristic Phrase):     │
│ [▶️ Ni Re Ga Ma# Dha Ni Sa]        │
│                                     │
│ Practice Mode:                      │
│ [🎯 Aroha] [🎯 Avaroha] [🎯 Pakad] │
│                                     │
│ Related Patterns:                   │
│ • Evening bird songs (similarity)   │
│ • Golden ratio in phrase structure  │
├─────────────────────────────────────┤
│ [🏠] [🎵] [🌿] [📊] [👤]          │
└─────────────────────────────────────┘
```

**Interaction Patterns:**
- Swipe between ragas for quick comparison
- Tap notes for individual playback
- Long-press for detailed note information
- Gesture-based aroha/avaroha practice

### 4. Tala Cycles

**Primary Elements:**
- **Tala Wheel**: Circular representation of beat cycles
- **Sam/Khali Indicators**: Visual distinction for strong/empty beats
- **Tempo Slider**: 60-200 BPM adjustment
- **Tabla Sounds**: Authentic percussion samples
- **Cycle Counter**: Visual beat progression

**Layout Structure:**
```
┌─────────────────────────────────────┐
│ ← Tala Cycles              [🥁]     │
├─────────────────────────────────────┤
│ Selected: Teentaal (16 beats) ⌄     │
│                                     │
│        ┌─────────────────────┐      │
│        │   1 Sam        16   │      │
│        │ ●                ○  │      │
│        │2               15   │      │
│        │ ●     ┌─────┐     ○│      │
│        │3    4 │120BPM│ 14 13│      │
│        │ ●   ● │     │ ○  ○ │      │
│        │      └─────┘       │      │
│        │5 ●  ●     ●    ● 12│      │
│        │   6  7   8   9 Khali│      │
│        └─────────────────────┘      │
│                                     │
│ Tempo: [────●────] 120 BPM         │
│                                     │
│ [▶️] [⏸️] [⏹️]     [🔄 Loop]      │
│                                     │
│ Subdivisions: [Dha] [Dhin] [Ta] [Tin]│
├─────────────────────────────────────┤
│ [🏠] [🎵] [🌿] [📊] [👤]          │
└─────────────────────────────────────┘
```

**Interaction Patterns:**
- Tap beats to trigger tabla sounds
- Swipe to change tala types
- Pinch for tempo adjustment
- Shake device to reset cycle

### 5. Natural Pattern Explorer

**Primary Elements:**
- **Pattern Categories**: Ocean, Birds, Wind, Rain, etc.
- **Waveform Previews**: Visual representations of natural sounds
- **Filter Controls**: Category-based search and filtering
- **Metadata Display**: Location, context, frequencies
- **Musical Connections**: Related ragas and cultural significance

**Layout Structure:**
```
┌─────────────────────────────────────┐
│ ← Nature Explorer          [🔍]     │
├─────────────────────────────────────┤
│ Categories:                         │
│ [🌊 Ocean] [🐦 Birds] [🌬️ Wind]    │
│ [🌧️ Rain] [🌊 All] [⭐ Featured]   │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 🌊 Pacific Ocean Waves          │ │
│ │ ~~~~~~~~~~~~~~~~~~~~~~~~        │ │
│ │ Location: Malibu, CA            │ │
│ │ Frequency: 0.1-2 Hz             │ │
│ │ [▶️] [📊] [🎵 Musical Links]    │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 🐦 Dawn Chorus - Indian Robin   │ │
│ │ ♪♫♪♫♪♫♪♫♪♫♪♫♪♫♪♫             │ │
│ │ Location: Kerala, India         │ │
│ │ Frequency: 2-8 kHz              │ │
│ │ [▶️] [📊] [🎵 Musical Links]    │ │
│ └─────────────────────────────────┘ │
│                                     │
│ Pattern Discoveries: 12             │
│ [🔄 Find Similar] [📚 Learn More]  │
├─────────────────────────────────────┤
│ [🏠] [🎵] [🌿] [📊] [👤]          │
└─────────────────────────────────────┘
```

**Interaction Patterns:**
- Horizontal scroll for pattern browsing
- Vertical swipe for category switching
- Tap waveforms for audio playback
- Pinch waveforms for detailed analysis

### 6. Pattern Correlation Analysis

**Primary Elements:**
- **Similarity Heatmap**: Visual correlation matrix
- **Side-by-Side Comparison**: Dual audio playback
- **Mathematical Relationships**: Fibonacci, golden ratio indicators
- **Cross-Pattern Discovery**: Related patterns across categories
- **Educational Context**: Explanatory text and examples

**Layout Structure:**
```
┌─────────────────────────────────────┐
│ ← Pattern Analysis         [📊]     │
├─────────────────────────────────────┤
│ Comparing: Ocean Waves & Raga Yaman │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ Similarity Score: 0.73          │ │
│ │ ████████████████████████████▒▒▒ │ │
│ │                                 │ │
│ │ Common Patterns:                │ │
│ │ • Golden ratio in wave spacing  │ │
│ │ • Fibonacci in melodic intervals│ │
│ │ • Harmonic resonance at 440Hz   │ │
│ └─────────────────────────────────┘ │
│                                     │
│ Side-by-Side Playback:              │
│ ┌─────────────┐ ┌─────────────────┐ │
│ │ 🌊 Ocean    │ │ 🎵 Raga Yaman   │ │
│ │ [▶️] [📊]   │ │ [▶️] [📊]       │ │
│ └─────────────┘ └─────────────────┘ │
│                                     │
│ [🔄 Sync Play] [📈 Detailed Analysis]│
│                                     │
│ Related Discoveries:                │
│ • Shell spirals → Tala cycles       │
│ • Bird songs → Shruti intervals     │
├─────────────────────────────────────┤
│ [🏠] [🎵] [🌿] [📊] [👤]          │
└─────────────────────────────────────┘
```

**Interaction Patterns:**
- Synchronized playback with visual correlation
- Scrub through analysis timeline
- Tap correlation points for detailed explanation
- Share discoveries via social features

### 7. Visualizations Dashboard

**Primary Elements:**
- **Waveform Display**: Real-time audio visualization
- **Frequency Spectrum**: FFT analysis with peak indicators
- **Pattern Overlay**: Mathematical relationship visualization
- **3D Spiral View**: Golden ratio and Fibonacci representations
- **Harmonic Series**: Overtone relationship display

**Layout Structure:**
```
┌─────────────────────────────────────┐
│ ← Visualizations           [🎨]     │
├─────────────────────────────────────┤
│ View: [Waveform] [Spectrum] [3D] ⌄  │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ Real-time Waveform              │ │
│ │ ~~~~∩~~~~∩~~~~∩~~~~∩~~~~∩~~~~  │ │
│ │                                 │ │
│ │ Frequency Spectrum              │ │
│ │ ||||||||||||||||||||||||||||    │ │
│ │ 0Hz    440Hz   880Hz   1.76kHz  │ │
│ │                                 │ │
│ │ Golden Ratio: φ = 1.618         │ │
│ │ Fibonacci: 1,1,2,3,5,8,13...    │ │
│ └─────────────────────────────────┘ │
│                                     │
│ Controls:                           │
│ [⏸️] [🔄 Loop] [📸 Capture]        │
│                                     │
│ Analysis Options:                   │
│ [✓] Show Harmonics                  │
│ [✓] Pattern Overlay                 │
│ [✓] Mathematical Grid               │
│ [ ] Export Data                     │
├─────────────────────────────────────┤
│ [🏠] [🎵] [🌿] [📊] [👤]          │
└─────────────────────────────────────┘
```

**Interaction Patterns:**
- Pinch-to-zoom for detailed frequency analysis
- Rotate device for 3D visualization mode
- Tap spectrum peaks for harmonic information
- Long-press to capture analysis snapshots

### 8. Settings & Profile

**Primary Elements:**
- **Audio Preferences**: Sample rate, buffer size, output device
- **Accessibility Options**: Screen reader support, high contrast, text size
- **Learning Progress**: Achievements, patterns discovered, session history
- **Cultural Context**: Language preferences, notation system
- **Privacy Settings**: Data sharing, offline mode, analytics

**Layout Structure:**
```
┌─────────────────────────────────────┐
│ ← Settings                 [👤]     │
├─────────────────────────────────────┤
│ Profile: Music Explorer             │
│ Patterns Discovered: 47             │
│ Hours Practiced: 23.5               │
│                                     │
│ ⚙️ Audio Settings                   │
│ • Sample Rate: 44.1kHz              │
│ • Buffer Size: 512 samples          │
│ • Output Device: Built-in Speaker   │
│                                     │
│ ♿ Accessibility                     │
│ • High Contrast Mode    [Toggle]    │
│ • Large Text Size       [Toggle]    │
│ • Screen Reader Support [Toggle]    │
│ • Haptic Feedback       [Toggle]    │
│                                     │
│ 🌍 Cultural Context                 │
│ • Language: English                 │
│ • Notation: Western/Indian          │
│ • Raga Names: Sanskrit/English      │
│                                     │
│ 🔒 Privacy & Data                   │
│ • Offline Mode          [Toggle]    │
│ • Analytics Sharing     [Toggle]    │
│ • Performance Data      [Toggle]    │
├─────────────────────────────────────┤
│ [🏠] [🎵] [🌿] [📊] [👤]          │
└─────────────────────────────────────┘
```

---

## Design System Guidelines

### Visual Design Language

**Color Palette:**
- **Primary**: Deep Ocean Blue (#1e40af) - Stability, depth
- **Secondary**: Golden Yellow (#f59e0b) - Illumination, discovery
- **Accent**: Coral Pink (#f472b6) - Energy, creativity
- **Background**: Warm White (#fefefe) / Charcoal (#1f2937) for dark mode
- **Text**: Near Black (#111827) / Off White (#f9fafb) for dark mode

**Typography:**
- **Headers**: Inter Bold (24px, 20px, 18px)
- **Body**: Inter Regular (16px, 14px)
- **Captions**: Inter Medium (12px, 10px)
- **Monospace**: JetBrains Mono (for frequency display)

**Spacing System:**
- Base unit: 8px
- Micro: 4px, Small: 8px, Medium: 16px, Large: 24px, XL: 32px

### Interaction Design

**Touch Targets:**
- Minimum 44px × 44px for all interactive elements
- 8px minimum spacing between targets
- Visual feedback within 100ms of touch

**Gestures:**
- **Tap**: Primary selection and playback
- **Long Press**: Context menus and detailed information
- **Swipe**: Navigation and browsing
- **Pinch**: Zoom and precision adjustment
- **Drag**: Continuous parameter adjustment

**Animations:**
- Duration: 200-300ms for UI transitions
- Easing: Cubic-bezier(0.25, 0.46, 0.45, 0.94)
- Audio visualizations: 60fps real-time rendering

### Accessibility Features

**WCAG 2.1 AA Compliance:**
- Color contrast ratio ≥ 4.5:1 for normal text
- Color contrast ratio ≥ 3:1 for large text
- Alternative text for all images and visualizations
- Keyboard navigation support
- Screen reader compatibility

**Audio Accessibility:**
- Visual indicators for all audio feedback
- Haptic feedback for tactile confirmation
- Adjustable audio levels and frequency ranges
- Captions for educational audio content

---

## Technical Considerations

### Performance Requirements

**Audio Engine:**
- Web Audio API with fallback to HTML5 Audio
- 512-sample buffer size for low latency
- 44.1kHz sample rate standard
- Cross-browser compatibility testing

**Pattern Analysis:**
- Client-side FFT processing using Web Workers
- Efficient frequency correlation algorithms
- Memory management for large pattern datasets
- Progressive loading for educational content

**Offline Capability:**
- Service Worker for core functionality
- IndexedDB for pattern and audio data storage
- Selective sync for updated content
- 25MB maximum offline storage

### Data Architecture

**Pattern Database:**
- 20+ natural sound patterns with metadata
- Compressed audio files (Opus/AAC)
- Mathematical relationship data
- Cultural context and educational material

**User Data:**
- Learning progress and achievements
- Custom pattern discoveries
- Audio preferences and settings
- Privacy-compliant analytics

---

## User Experience Flow

### Primary User Journey

1. **Discovery**: User opens app, sees featured pattern of the day
2. **Exploration**: Navigates to Natural Pattern Explorer
3. **Selection**: Chooses interesting natural sound (e.g., ocean waves)
4. **Analysis**: Views pattern correlation with musical elements
5. **Musical Connection**: Explores related raga (e.g., Raga Yaman)
6. **Practice**: Uses Audio Engine to play shruti and practice
7. **Visualization**: Sees mathematical relationships in real-time
8. **Learning**: Accesses educational context and cultural significance
9. **Sharing**: Saves discovery and potentially shares with others
10. **Return**: Bookmarks pattern for future exploration

### Secondary Flows

**Educational Path:**
- Guided tutorials for each major feature
- Progressive skill building with achievements
- Cultural context modules
- Expert validation content

**Creative Path:**
- Pattern composition tools
- Custom correlation discovery
- Audio export capabilities
- Integration with external music software

**Research Path:**
- Detailed mathematical analysis
- Data export for academic use
- Citation and reference tools
- Community contribution features

---

## Success Metrics

### User Engagement
- >70% user engagement with musical connection examples
- Users explore >3 natural patterns per session
- Session duration >10 minutes average
- Return rate >60% within 7 days

### Technical Performance
- <2 seconds pattern correlation loading time
- <100MB memory usage during operation
- 99.9% uptime for core audio functionality
- <200ms audio latency for real-time features

### Educational Impact
- User completion rate >80% for tutorial modules
- Pattern discovery rate >5 new patterns per user
- Cultural authenticity validation >95% accuracy
- User satisfaction score >4.2/5.0

---

## Development Phases

### Phase 1: Core Audio Foundation (Weeks 1-4)
- Web Audio API implementation
- 22-shruti system with visual interface
- Basic raga and tala functionality
- Mobile-responsive UI framework

### Phase 2: Pattern Recognition (Weeks 5-8)
- Natural sound pattern database
- Mathematical correlation algorithms
- Basic visualization tools
- Pattern discovery interface

### Phase 3: Advanced Features (Weeks 9-12)
- Cross-pattern correlation system
- Educational content integration
- Advanced visualizations
- Performance optimization

### Phase 4: Polish & Launch (Weeks 13-16)
- Accessibility compliance testing
- Cultural authenticity validation
- Performance optimization
- User testing and feedback integration

---

This wireframe specification provides a comprehensive foundation for developing the Cosmic mobile app while maintaining focus on the core musical, mathematical, and natural pattern exploration features outlined in your MVP requirements.