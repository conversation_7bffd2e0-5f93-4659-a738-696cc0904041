'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TreePine, 
  Waves, 
  TrendingUp, 
  RotateCcw, 
  Zap,
  ArrowRight,
  ArrowUpRight,
  ArrowDownRight,
  Circle,
  Triangle,
  Square,
  Hexagon,
  Play,
  ExternalLink,
  Info,
  Filter,
  Search
} from 'lucide-react';

import { 
  PatternRelative, 
  PatternRelativeType, 
  MathematicalPatternType,
  NaturalSoundCategory 
} from '@/types/NaturalPattern.types';

interface PatternRelativesProps {
  relatives: PatternRelative[];
  className?: string;
  onPatternSelect?: (patternId: string) => void;
}

interface RelativeCardProps {
  relative: PatternRelative;
  onSelect?: () => void;
  onPlayPattern?: () => void;
  isSelected?: boolean;
}

// Pattern type icons and colors
const patternTypeStyles = {
  [MathematicalPatternType.FIBONACCI]: {
    icon: RotateCcw,
    color: 'from-yellow-500/20 to-amber-500/20 border-yellow-400/30',
    textColor: 'text-yellow-300',
    bgColor: 'bg-yellow-500/20',
  },
  [MathematicalPatternType.GOLDEN_RATIO]: {
    icon: Triangle,
    color: 'from-orange-500/20 to-red-500/20 border-orange-400/30',
    textColor: 'text-orange-300',
    bgColor: 'bg-orange-500/20',
  },
  [MathematicalPatternType.GEOMETRIC_PROGRESSION]: {
    icon: TrendingUp,
    color: 'from-blue-500/20 to-indigo-500/20 border-blue-400/30',
    textColor: 'text-blue-300',
    bgColor: 'bg-blue-500/20',
  },
  [MathematicalPatternType.HARMONIC_SERIES]: {
    icon: Waves,
    color: 'from-green-500/20 to-emerald-500/20 border-green-400/30',
    textColor: 'text-green-300',
    bgColor: 'bg-green-500/20',
  },
  [MathematicalPatternType.FRACTAL]: {
    icon: Hexagon,
    color: 'from-purple-500/20 to-pink-500/20 border-purple-400/30',
    textColor: 'text-purple-300',
    bgColor: 'bg-purple-500/20',
  },
  [MathematicalPatternType.PRIME_SEQUENCE]: {
    icon: Square,
    color: 'from-cyan-500/20 to-teal-500/20 border-cyan-400/30',
    textColor: 'text-cyan-300',
    bgColor: 'bg-cyan-500/20',
  },
};

// Relationship type arrows
const relationshipArrows = {
  [PatternRelativeType.NATURAL_PHENOMENON]: ArrowRight,
  [PatternRelativeType.MUSICAL_PATTERN]: ArrowUpRight,
  [PatternRelativeType.MATHEMATICAL_SEQUENCE]: ArrowDownRight,
  [PatternRelativeType.FRACTAL_STRUCTURE]: Circle,
  [PatternRelativeType.BIOLOGICAL_RHYTHM]: ArrowRight,
  [PatternRelativeType.GEOMETRIC_FORM]: Circle,
};

// Relationship descriptions
const relationshipDescriptions = {
  [PatternRelativeType.NATURAL_PHENOMENON]: 'Related natural phenomenon or occurrence',
  [PatternRelativeType.MUSICAL_PATTERN]: 'Connected musical pattern or structure',
  [PatternRelativeType.MATHEMATICAL_SEQUENCE]: 'Mathematical sequence or formula',
  [PatternRelativeType.FRACTAL_STRUCTURE]: 'Fractal or self-similar structure',
  [PatternRelativeType.BIOLOGICAL_RHYTHM]: 'Biological rhythm or cycle',
  [PatternRelativeType.GEOMETRIC_FORM]: 'Geometric form or shape',
};

const RelativeCard: React.FC<RelativeCardProps> = ({
  relative,
  onSelect,
  onPlayPattern,
  isSelected = false,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  const patternStyle = patternTypeStyles[PatternRelativeType.MATHEMATICAL_SEQUENCE];
  const RelationshipArrow = relationshipArrows[relative.type];
  const PatternIcon = patternStyle.icon;

  const formatSimilarity = (similarity: number): string => {
    return `${Math.round(similarity * 100)}%`;
  };

  const formatMathematicalValue = (value: number): string => {
    if (value < 0.01) return value.toExponential(2);
    if (value < 1) return value.toFixed(3);
    if (value < 100) return value.toFixed(2);
    return Math.round(value).toString();
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      whileHover={{ scale: 1.02 }}
      className={`
        relative cursor-pointer rounded-xl border backdrop-blur-sm transition-all duration-300
        ${isSelected 
          ? `bg-gradient-to-br ${patternStyle.color} ring-2 ring-cosmic-400 shadow-lg shadow-cosmic-400/20` 
          : `bg-gradient-to-br ${patternStyle.color} hover:shadow-lg hover:shadow-white/10`
        }
      `}
      onClick={onSelect}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Relationship indicator */}
      <div className="absolute -top-2 -right-2 z-10">
        <div className={`p-1 rounded-full ${patternStyle.bgColor} border border-white/20`}>
          <RelationshipArrow className="w-3 h-3 text-white" />
        </div>
      </div>

      <div className="p-4">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${patternStyle.bgColor}`}>
              <PatternIcon className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="font-semibold text-white text-sm truncate">
                {relative.name}
              </h4>
              <p className={`text-xs ${patternStyle.textColor} capitalize`}>
                {relative.type.replace('_', ' ')} Pattern
              </p>
            </div>
          </div>
        </div>

        {/* Description */}
        <p className="text-white/80 text-xs mb-3 line-clamp-2">
          {relative.description}
        </p>

        {/* Pattern category */}
        <div className="mb-3 p-2 bg-white/5 rounded-lg">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-white/80">Category</span>
            <span className="text-xs text-white/60 capitalize">
              {relative.category?.replace('_', ' ') || 'General'}
            </span>
          </div>
        </div>

        {/* Similarity score */}
        <div className="mb-3">
          <div className="flex items-center justify-between text-xs mb-1">
            <span className="text-white/60">Similarity</span>
            <span className={patternStyle.textColor}>
              {formatSimilarity(relative.similarity)}
            </span>
          </div>
          <div className="w-full bg-white/10 rounded-full h-2">
            <motion.div
              className={`h-2 rounded-full bg-gradient-to-r ${patternStyle.color.replace('/20', '/60')}`}
              initial={{ width: 0 }}
              animate={{ width: `${relative.similarity * 100}%` }}
              transition={{ duration: 1, delay: 0.2 }}
            />
          </div>
        </div>

        {/* Visualization data */}
        {relative.visualizationData && (
          <div className="mb-3">
            <div className="text-xs text-white/60 mb-1">Visualization Available</div>
            <div className="flex items-center space-x-2 text-xs text-white/70">
              <TreePine className="w-3 h-3" />
              <span>Pattern visualization data included</span>
            </div>
          </div>
        )}

        {/* Audio sample */}
        {relative.audioUrl && (
          <div className="mb-3">
            <button
              onClick={() => onPlayPattern?.(relative.audioUrl!)}
              className="flex items-center space-x-2 px-3 py-1 bg-white/10 hover:bg-white/20 rounded-full text-xs text-white/80 transition-colors"
              title="Play pattern audio"
            >
              <Play className="w-3 h-3" />
              <span>Play Pattern</span>
            </button>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {onPlayPattern && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onPlayPattern();
                }}
                className="p-1 text-white/60 hover:text-white transition-colors"
                title="Play pattern"
              >
                <Play className="w-3 h-3" />
              </button>
            )}
            
            {relative.externalUrl && (
              <a
                href={relative.externalUrl}
                target="_blank"
                rel="noopener noreferrer"
                onClick={(e) => e.stopPropagation()}
                className="p-1 text-white/60 hover:text-white transition-colors"
                title="Learn more"
              >
                <ExternalLink className="w-3 h-3" />
              </a>
            )}
          </div>

          <div className="text-xs text-white/40">
            {relationshipDescriptions[relative.relationshipType]}
          </div>
        </div>

        {/* Hover overlay */}
        <AnimatePresence>
          {isHovered && !isSelected && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-white/5 rounded-xl flex items-center justify-center"
            >
              <div className="flex items-center space-x-2 text-white">
                <Info className="w-4 h-4" />
                <span className="text-sm font-medium">View Details</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export const PatternRelatives: React.FC<PatternRelativesProps> = ({
  relatives,
  className = '',
  onPatternSelect,
}) => {
  const [selectedRelative, setSelectedRelative] = useState<string | null>(null);
  const [filterType, setFilterType] = useState<PatternRelativeType | 'all'>('all');
  const [sortBy, setSortBy] = useState<'similarity' | 'name' | 'type'>('similarity');
  const [searchQuery, setSearchQuery] = useState('');

  // Filter and sort relatives
  const processedRelatives = useMemo(() => {
    let filtered = relatives;

    // Filter by relationship type
    if (filterType !== 'all') {
      filtered = filtered.filter(rel => rel.type === filterType);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(rel =>
        rel.name.toLowerCase().includes(query) ||
        rel.description.toLowerCase().includes(query)
      );
    }

    // Sort
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'similarity':
          return b.similarity - a.similarity;
        case 'name':
          return a.name.localeCompare(b.name);
        case 'type':
          return a.relationshipType.localeCompare(b.relationshipType);
        default:
          return 0;
      }
    });
  }, [relatives, filterType, filterMathType, sortBy, searchQuery]);

  // Group by relationship type for better organization
  const groupedRelatives = useMemo(() => {
    const groups: Record<string, PatternRelative[]> = {};
    
    processedRelatives.forEach(relative => {
      const type = relative.relationshipType;
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(relative);
    });

    return groups;
  }, [processedRelatives]);

  if (relatives.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <TreePine className="w-12 h-12 mx-auto mb-4 text-white/40" />
        <h4 className="text-lg font-medium text-white/60 mb-2">
          No Pattern Relatives
        </h4>
        <p className="text-white/40 text-sm">
          Related patterns will appear here when mathematical connections are discovered
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header and Controls */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-white mb-1">
              Pattern Relatives
            </h3>
            <p className="text-white/60 text-sm">
              {processedRelatives.length} related pattern{processedRelatives.length !== 1 ? 's' : ''} found
            </p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-wrap gap-3">
          {/* Search */}
          <div className="relative flex-1 min-w-48">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" />
            <input
              type="text"
              placeholder="Search patterns..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg 
                       text-white placeholder-white/50 focus:outline-none focus:ring-2 
                       focus:ring-cosmic-400 focus:border-transparent text-sm"
            />
          </div>

          {/* Relationship type filter */}
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-sm text-white"
          >
            <option value="all">All Relationships</option>
            {Object.values(PatternRelativeType).map(type => (
              <option key={type} value={type}>
                {type.replace('_', ' ')}
              </option>
            ))}
          </select>

          {/* Mathematical type filter */}
          <select
            value={filterMathType}
            onChange={(e) => setFilterMathType(e.target.value as any)}
            className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-sm text-white"
          >
            <option value="all">All Math Types</option>
            {Object.values(MathematicalPatternType).map(type => (
              <option key={type} value={type}>
                {type.replace('_', ' ')}
              </option>
            ))}
          </select>

          {/* Sort by */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-sm text-white"
          >
            <option value="similarity">Similarity</option>
            <option value="name">Name</option>
            <option value="type">Type</option>
          </select>
        </div>
      </div>

      {/* Pattern Relatives Grid */}
      <div className="space-y-6">
        {Object.entries(groupedRelatives).map(([relationshipType, typeRelatives]) => (
          <div key={relationshipType}>
            {/* Group Header */}
            <div className="flex items-center space-x-2 mb-3">
              {React.createElement(relationshipArrows[relationshipType as PatternRelativeType], {
                className: "w-4 h-4 text-white/60"
              })}
              <h4 className="text-sm font-medium text-white/80 capitalize">
                {relationshipType.replace('_', ' ')} Patterns ({typeRelatives.length})
              </h4>
            </div>

            {/* Pattern Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <AnimatePresence>
                {typeRelatives.map((relative, index) => (
                  <RelativeCard
                    key={`${relative.name}-${index}`}
                    relative={relative}
                    isSelected={selectedRelative === relative.name}
                    onSelect={() => {
                      setSelectedRelative(relative.name);
                      if (onPatternSelect && relative.sourcePattern?.id) {
                        onPatternSelect(relative.sourcePattern.id);
                      }
                    }}
                    onPlayPattern={() => {
                      console.log('Playing pattern:', relative.name);
                      // This would integrate with the audio engine
                    }}
                  />
                ))}
              </AnimatePresence>
            </div>
          </div>
        ))}
      </div>

      {processedRelatives.length === 0 && (relatives.length > 0) && (
        <div className="text-center py-8">
          <Filter className="w-8 h-8 mx-auto mb-2 text-white/40" />
          <p className="text-white/60 text-sm">
            No patterns match the current filters
          </p>
        </div>
      )}
    </div>
  );
};
