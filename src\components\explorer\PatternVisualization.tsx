'use client';

import React, { useEffect, useRef, useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BarChart3, 
  Activity, 
  Waves, 
  TrendingUp,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2,
  Settings,
  Loader2
} from 'lucide-react';

import { NaturalSoundPattern } from '@/types/NaturalPattern.types';

interface PatternVisualizationProps {
  pattern: NaturalSoundPattern;
  analysisResults?: any[];
  isAnalyzing?: boolean;
  className?: string;
}

interface VisualizationConfig {
  showWaveform: boolean;
  showSpectrum: boolean;
  showPatterns: boolean;
  showHarmonics: boolean;
  colorScheme: 'cosmic' | 'natural' | 'monochrome';
  sensitivity: number;
}

interface WaveformCanvasProps {
  waveformData: Float32Array;
  width: number;
  height: number;
  color: string;
  isAnimated?: boolean;
}

const WaveformCanvas: React.FC<WaveformCanvasProps> = ({
  waveformData,
  width,
  height,
  color,
  isAnimated = false,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const drawWaveform = (offset = 0) => {
      ctx.clearRect(0, 0, width, height);
      
      // Set up drawing style
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      // Create gradient for visual appeal
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, color);
      gradient.addColorStop(1, color + '40');
      ctx.strokeStyle = gradient;

      // Draw waveform
      ctx.beginPath();
      const sliceWidth = width / waveformData.length;
      let x = 0;

      for (let i = 0; i < waveformData.length; i++) {
        const dataIndex = isAnimated ? (i + offset) % waveformData.length : i;
        const v = waveformData[dataIndex] * 0.5;
        const y = (v * height / 2) + (height / 2);

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }

        x += sliceWidth;
      }

      ctx.stroke();

      // Add glow effect
      ctx.shadowColor = color;
      ctx.shadowBlur = 10;
      ctx.stroke();
      ctx.shadowBlur = 0;
    };

    if (isAnimated) {
      let offset = 0;
      const animate = () => {
        drawWaveform(offset);
        offset += 2;
        animationRef.current = requestAnimationFrame(animate);
      };
      animate();
    } else {
      drawWaveform();
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [waveformData, width, height, color, isAnimated]);

  return (
    <canvas
      ref={canvasRef}
      width={width}
      height={height}
      className="rounded-lg"
    />
  );
};

interface SpectrumAnalyzerProps {
  frequencyData: {
    frequencyBins: number[];
    magnitudes: number[];
  };
  width: number;
  height: number;
  colorScheme: string;
}

const SpectrumAnalyzer: React.FC<SpectrumAnalyzerProps> = ({
  frequencyData,
  width,
  height,
  colorScheme,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, width, height);

    const barWidth = width / frequencyData.magnitudes.length;
    const maxMagnitude = Math.max(...frequencyData.magnitudes);

    frequencyData.magnitudes.forEach((magnitude, index) => {
      const barHeight = (magnitude / maxMagnitude) * height;
      const x = index * barWidth;
      const y = height - barHeight;

      // Create gradient based on frequency
      const hue = (index / frequencyData.magnitudes.length) * 240; // Blue to red
      const gradient = ctx.createLinearGradient(0, y, 0, height);
      
      if (colorScheme === 'cosmic') {
        gradient.addColorStop(0, `hsl(${240 + hue * 0.5}, 70%, 60%)`);
        gradient.addColorStop(1, `hsl(${240 + hue * 0.5}, 70%, 30%)`);
      } else {
        gradient.addColorStop(0, `hsl(${hue}, 70%, 60%)`);
        gradient.addColorStop(1, `hsl(${hue}, 70%, 30%)`);
      }

      ctx.fillStyle = gradient;
      ctx.fillRect(x, y, barWidth - 1, barHeight);
    });
  }, [frequencyData, width, height, colorScheme]);

  return (
    <canvas
      ref={canvasRef}
      width={width}
      height={height}
      className="rounded-lg"
    />
  );
};

export const PatternVisualization: React.FC<PatternVisualizationProps> = ({
  pattern,
  analysisResults = [],
  isAnalyzing = false,
  className = '',
}) => {
  const [config, setConfig] = useState<VisualizationConfig>({
    showWaveform: true,
    showSpectrum: true,
    showPatterns: true,
    showHarmonics: false,
    colorScheme: 'cosmic',
    sensitivity: 0.7,
  });
  const [isExpanded, setIsExpanded] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 800, height: 200 });

  // Update dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setDimensions({
          width: rect.width - 32, // Account for padding
          height: isExpanded ? 400 : 200,
        });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [isExpanded]);

  // Color scheme mapping
  const colorSchemes = {
    cosmic: '#8B5CF6',
    natural: '#10B981',
    monochrome: '#6B7280',
  };

  const primaryColor = colorSchemes[config.colorScheme];

  // Prepare visualization data
  const visualizationData = useMemo(() => {
    return {
      waveform: pattern.waveformData || new Float32Array(1024).map(() => Math.random() * 2 - 1),
      spectrum: {
        frequencyBins: pattern.frequencyData?.frequencyBins || [],
        magnitudes: pattern.frequencyData?.magnitudes || [],
      },
      harmonics: pattern.metadata.harmonicSeries || [],
      patterns: analysisResults,
    };
  }, [pattern, analysisResults]);

  return (
    <div className={`glass ${className}`} ref={containerRef}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-cosmic-500/20 rounded-lg">
            <Activity className="w-5 h-5 text-cosmic-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">
              Pattern Visualization
            </h3>
            <p className="text-white/60 text-sm">
              Real-time analysis of {pattern.name}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {isAnalyzing && (
            <div className="flex items-center space-x-2 text-cosmic-400">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">Analyzing...</span>
            </div>
          )}

          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 text-white/70 hover:text-white transition-colors"
            title="Visualization settings"
          >
            <Settings className="w-4 h-4" />
          </button>

          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 text-white/70 hover:text-white transition-colors"
            title={isExpanded ? 'Minimize' : 'Expand'}
          >
            {isExpanded ? (
              <Minimize2 className="w-4 h-4" />
            ) : (
              <Maximize2 className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* Settings Panel */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border-b border-white/10 p-4 bg-white/5"
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {/* Visualization toggles */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-white/80">Display</label>
                {[
                  { key: 'showWaveform', label: 'Waveform', icon: Waves },
                  { key: 'showSpectrum', label: 'Spectrum', icon: BarChart3 },
                  { key: 'showPatterns', label: 'Patterns', icon: TrendingUp },
                ].map(({ key, label, icon: Icon }) => (
                  <label key={key} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={config[key as keyof VisualizationConfig] as boolean}
                      onChange={(e) => setConfig(prev => ({ ...prev, [key]: e.target.checked }))}
                      className="rounded border-white/20 bg-white/10 text-cosmic-500 focus:ring-cosmic-400"
                    />
                    <Icon className="w-3 h-3 text-white/60" />
                    <span className="text-sm text-white/80">{label}</span>
                  </label>
                ))}
              </div>

              {/* Color scheme */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-white/80">Color Scheme</label>
                <select
                  value={config.colorScheme}
                  onChange={(e) => setConfig(prev => ({ ...prev, colorScheme: e.target.value as any }))}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-sm text-white"
                >
                  <option value="cosmic">Cosmic</option>
                  <option value="natural">Natural</option>
                  <option value="monochrome">Monochrome</option>
                </select>
              </div>

              {/* Sensitivity */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-white/80">Sensitivity</label>
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={config.sensitivity}
                  onChange={(e) => setConfig(prev => ({ ...prev, sensitivity: parseFloat(e.target.value) }))}
                  className="w-full"
                />
                <span className="text-xs text-white/60">{Math.round(config.sensitivity * 100)}%</span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Visualization Content */}
      <div className="p-4 space-y-4">
        {/* Waveform */}
        {config.showWaveform && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-2"
          >
            <div className="flex items-center space-x-2">
              <Waves className="w-4 h-4 text-white/60" />
              <span className="text-sm font-medium text-white/80">Waveform</span>
            </div>
            <div className="bg-black/20 rounded-lg p-2">
              <WaveformCanvas
                waveformData={visualizationData.waveform}
                width={dimensions.width - 16}
                height={100}
                color={primaryColor}
                isAnimated={isAnalyzing}
              />
            </div>
          </motion.div>
        )}

        {/* Spectrum */}
        {config.showSpectrum && visualizationData.spectrum.magnitudes.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-2"
          >
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4 text-white/60" />
              <span className="text-sm font-medium text-white/80">Frequency Spectrum</span>
            </div>
            <div className="bg-black/20 rounded-lg p-2">
              <SpectrumAnalyzer
                frequencyData={visualizationData.spectrum}
                width={dimensions.width - 16}
                height={120}
                colorScheme={config.colorScheme}
              />
            </div>
          </motion.div>
        )}

        {/* Pattern Analysis Results */}
        {config.showPatterns && analysisResults.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-2"
          >
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-white/60" />
              <span className="text-sm font-medium text-white/80">Detected Patterns</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {analysisResults.map((result, index) => (
                <div key={index} className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-white">
                      {result.type || 'Pattern'}
                    </span>
                    <span className="text-xs text-cosmic-400">
                      {Math.round((result.confidence || 0) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-cosmic-400 to-cosmic-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(result.confidence || 0) * 100}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Empty state */}
        {!isAnalyzing && analysisResults.length === 0 && (
          <div className="text-center py-8">
            <Activity className="w-12 h-12 mx-auto mb-4 text-white/40" />
            <h4 className="text-lg font-medium text-white/60 mb-2">
              No Analysis Data
            </h4>
            <p className="text-white/40 text-sm">
              Pattern analysis will appear here when available
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
