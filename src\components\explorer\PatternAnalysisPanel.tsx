'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Zap, 
  Play, 
  Pause, 
  RotateCcw, 
  Download,
  Share2,
  <PERSON>tings,
  TrendingUp,
  BarChart3,
  PieChart,
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  Clock,
  Target,
  Layers
} from 'lucide-react';

interface AnalysisState {
  isAnalyzing: boolean;
  results: any[];
  error: string | null;
  progress: number;
}

interface PatternAnalysisPanelProps {
  analysisState: AnalysisState;
  onAnalyze: () => void;
  onExportResults?: (format: 'json' | 'csv' | 'pdf') => void;
  onShareResults?: () => void;
  className?: string;
}

interface AnalysisResultProps {
  result: any;
  index: number;
}

interface ProgressIndicatorProps {
  progress: number;
  isActive: boolean;
  label: string;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  isActive,
  label,
}) => {
  return (
    <div className="flex items-center space-x-3">
      <div className="relative w-8 h-8">
        <div className="absolute inset-0 rounded-full border-2 border-white/20" />
        <motion.div
          className="absolute inset-0 rounded-full border-2 border-cosmic-400"
          style={{
            background: `conic-gradient(from 0deg, #8B5CF6 ${progress * 3.6}deg, transparent ${progress * 3.6}deg)`,
          }}
          animate={{
            rotate: isActive ? 360 : 0,
          }}
          transition={{
            duration: 2,
            repeat: isActive ? Infinity : 0,
            ease: "linear",
          }}
        />
        <div className="absolute inset-2 rounded-full bg-cosmic-900 flex items-center justify-center">
          <span className="text-xs font-medium text-white">
            {Math.round(progress)}%
          </span>
        </div>
      </div>
      <div>
        <p className="text-sm font-medium text-white">{label}</p>
        <p className="text-xs text-white/60">
          {isActive ? 'Processing...' : progress === 100 ? 'Complete' : 'Pending'}
        </p>
      </div>
    </div>
  );
};

const AnalysisResult: React.FC<AnalysisResultProps> = ({ result, index }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getResultIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'fibonacci':
        return RotateCcw;
      case 'golden_ratio':
        return Target;
      case 'fractal':
        return Layers;
      case 'harmonic':
        return Activity;
      default:
        return TrendingUp;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-400';
    if (confidence >= 0.6) return 'text-yellow-400';
    if (confidence >= 0.4) return 'text-orange-400';
    return 'text-red-400';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.8) return 'High';
    if (confidence >= 0.6) return 'Medium';
    if (confidence >= 0.4) return 'Low';
    return 'Very Low';
  };

  const ResultIcon = getResultIcon(result.type || 'pattern');
  const confidence = result.confidence || 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="bg-white/5 rounded-lg border border-white/10 overflow-hidden"
    >
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-cosmic-500/20 rounded-lg">
              <ResultIcon className="w-4 h-4 text-cosmic-400" />
            </div>
            <div>
              <h4 className="font-semibold text-white text-sm capitalize">
                {(result.type || 'Pattern').replace('_', ' ')} Analysis
              </h4>
              <p className="text-xs text-white/60">
                {result.description || 'Mathematical pattern analysis'}
              </p>
            </div>
          </div>

          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 text-white/60 hover:text-white transition-colors"
          >
            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <TrendingUp className="w-4 h-4" />
            </motion.div>
          </button>
        </div>

        {/* Confidence Score */}
        <div className="mb-3">
          <div className="flex items-center justify-between text-sm mb-2">
            <span className="text-white/70">Confidence</span>
            <div className="flex items-center space-x-2">
              <span className={`font-medium ${getConfidenceColor(confidence)}`}>
                {getConfidenceLabel(confidence)}
              </span>
              <span className="text-white/60">
                {Math.round(confidence * 100)}%
              </span>
            </div>
          </div>
          <div className="w-full bg-white/10 rounded-full h-2">
            <motion.div
              className={`h-2 rounded-full ${
                confidence >= 0.8 ? 'bg-green-500' :
                confidence >= 0.6 ? 'bg-yellow-500' :
                confidence >= 0.4 ? 'bg-orange-500' : 'bg-red-500'
              }`}
              initial={{ width: 0 }}
              animate={{ width: `${confidence * 100}%` }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </div>
        </div>

        {/* Key Metrics */}
        {result.metrics && (
          <div className="grid grid-cols-2 gap-3 mb-3">
            {Object.entries(result.metrics).slice(0, 4).map(([key, value]) => (
              <div key={key} className="text-center p-2 bg-white/5 rounded-lg">
                <p className="text-xs text-white/60 capitalize mb-1">
                  {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                </p>
                <p className="text-sm font-medium text-white">
                  {typeof value === 'number' ? 
                    (value < 0.01 ? value.toExponential(2) : value.toFixed(3)) : 
                    String(value)
                  }
                </p>
              </div>
            ))}
          </div>
        )}

        {/* Status Indicator */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {confidence >= 0.6 ? (
              <CheckCircle className="w-4 h-4 text-green-400" />
            ) : confidence >= 0.3 ? (
              <AlertCircle className="w-4 h-4 text-yellow-400" />
            ) : (
              <XCircle className="w-4 h-4 text-red-400" />
            )}
            <span className="text-xs text-white/70">
              {confidence >= 0.6 ? 'Pattern Detected' : 
               confidence >= 0.3 ? 'Weak Pattern' : 'No Clear Pattern'}
            </span>
          </div>

          <div className="text-xs text-white/50">
            {result.processingTime ? `${result.processingTime}ms` : ''}
          </div>
        </div>

        {/* Expanded Details */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 pt-4 border-t border-white/10"
            >
              {/* Detailed Metrics */}
              {result.metrics && (
                <div className="mb-4">
                  <h5 className="text-sm font-medium text-white/80 mb-2">Detailed Metrics</h5>
                  <div className="space-y-2">
                    {Object.entries(result.metrics).map(([key, value]) => (
                      <div key={key} className="flex justify-between text-sm">
                        <span className="text-white/60 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                        </span>
                        <span className="text-white/80 font-mono">
                          {typeof value === 'number' ? 
                            (value < 0.01 ? value.toExponential(3) : value.toFixed(4)) : 
                            String(value)
                          }
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Algorithm Details */}
              {result.algorithm && (
                <div className="mb-4">
                  <h5 className="text-sm font-medium text-white/80 mb-2">Algorithm</h5>
                  <p className="text-sm text-white/70">{result.algorithm}</p>
                </div>
              )}

              {/* Parameters */}
              {result.parameters && (
                <div>
                  <h5 className="text-sm font-medium text-white/80 mb-2">Parameters</h5>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    {Object.entries(result.parameters).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="text-white/60">{key}:</span>
                        <span className="text-white/80 font-mono">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export const PatternAnalysisPanel: React.FC<PatternAnalysisPanelProps> = ({
  analysisState,
  onAnalyze,
  onExportResults,
  onShareResults,
  className = '',
}) => {
  const [analysisHistory, setAnalysisHistory] = useState<any[]>([]);
  const [showSettings, setShowSettings] = useState(false);

  const { isAnalyzing, results, error, progress } = analysisState;

  // Store completed analyses in history
  useEffect(() => {
    if (!isAnalyzing && results.length > 0 && progress === 100) {
      setAnalysisHistory(prev => [
        {
          timestamp: Date.now(),
          results: [...results],
          id: `analysis-${Date.now()}`,
        },
        ...prev.slice(0, 4), // Keep last 5 analyses
      ]);
    }
  }, [isAnalyzing, results, progress]);

  const analysisSteps = [
    { label: 'Preprocessing Audio', threshold: 20 },
    { label: 'Frequency Analysis', threshold: 40 },
    { label: 'Pattern Detection', threshold: 60 },
    { label: 'Mathematical Analysis', threshold: 80 },
    { label: 'Confidence Calculation', threshold: 100 },
  ];

  const getCurrentStep = () => {
    return analysisSteps.findIndex(step => progress < step.threshold);
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-cosmic-500/20 rounded-lg">
            <Zap className="w-5 h-5 text-cosmic-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">
              Pattern Analysis
            </h3>
            <p className="text-white/60 text-sm">
              Mathematical pattern detection and analysis
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {!isAnalyzing && results.length > 0 && (
            <>
              {onExportResults && (
                <div className="relative">
                  <button
                    onClick={() => onExportResults('json')}
                    className="p-2 text-white/70 hover:text-white transition-colors"
                    title="Export results"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              )}

              {onShareResults && (
                <button
                  onClick={onShareResults}
                  className="p-2 text-white/70 hover:text-white transition-colors"
                  title="Share results"
                >
                  <Share2 className="w-4 h-4" />
                </button>
              )}
            </>
          )}

          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 text-white/70 hover:text-white transition-colors"
            title="Analysis settings"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Settings Panel */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white/5 rounded-lg p-4 border border-white/10"
          >
            <h4 className="text-sm font-medium text-white/80 mb-3">Analysis Settings</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-xs text-white/60 mb-1 block">Sensitivity</label>
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  defaultValue="0.7"
                  className="w-full"
                />
              </div>
              <div>
                <label className="text-xs text-white/60 mb-1 block">Window Size</label>
                <select className="w-full bg-white/10 border border-white/20 rounded px-2 py-1 text-xs text-white">
                  <option value="1024">1024</option>
                  <option value="2048">2048</option>
                  <option value="4096">4096</option>
                </select>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Analysis Control */}
      <div className="glass p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h4 className="text-lg font-medium text-white mb-1">
              {isAnalyzing ? 'Analysis in Progress' : 'Ready to Analyze'}
            </h4>
            <p className="text-white/60 text-sm">
              {isAnalyzing 
                ? `Step ${getCurrentStep() + 1} of ${analysisSteps.length}: ${analysisSteps[getCurrentStep()]?.label || 'Processing'}`
                : 'Click analyze to detect mathematical patterns in the audio'
              }
            </p>
          </div>

          <motion.button
            onClick={onAnalyze}
            disabled={isAnalyzing}
            className={`
              px-6 py-3 rounded-lg font-medium transition-all duration-200
              ${isAnalyzing 
                ? 'bg-white/10 text-white/50 cursor-not-allowed' 
                : 'bg-gradient-to-r from-cosmic-400 to-cosmic-500 text-white hover:from-cosmic-500 hover:to-cosmic-600 shadow-lg hover:shadow-xl'
              }
            `}
            whileHover={!isAnalyzing ? { scale: 1.05 } : {}}
            whileTap={!isAnalyzing ? { scale: 0.95 } : {}}
          >
            {isAnalyzing ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Analyzing...</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4" />
                <span>Analyze Pattern</span>
              </div>
            )}
          </motion.button>
        </div>

        {/* Progress Indicators */}
        {isAnalyzing && (
          <div className="space-y-3">
            {analysisSteps.map((step, index) => (
              <ProgressIndicator
                key={step.label}
                progress={Math.max(0, Math.min(100, ((progress - (index * 20)) / 20) * 100))}
                isActive={getCurrentStep() === index}
                label={step.label}
              />
            ))}
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-500/10 border border-red-500/20 rounded-lg p-4"
        >
          <div className="flex items-center space-x-2 text-red-400">
            <XCircle className="w-5 h-5 flex-shrink-0" />
            <div>
              <h4 className="font-medium">Analysis Error</h4>
              <p className="text-sm text-red-300 mt-1">{error}</p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Current Results */}
      {!isAnalyzing && results.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-white">Analysis Results</h4>
          <div className="space-y-3">
            {results.map((result, index) => (
              <AnalysisResult key={index} result={result} index={index} />
            ))}
          </div>
        </div>
      )}

      {/* Analysis History */}
      {analysisHistory.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-white">Recent Analyses</h4>
          <div className="space-y-3">
            {analysisHistory.map((analysis) => (
              <div key={analysis.id} className="bg-white/5 rounded-lg p-4 border border-white/10">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-white/60" />
                    <span className="text-sm text-white/80">
                      {formatTimestamp(analysis.timestamp)}
                    </span>
                  </div>
                  <span className="text-xs text-white/60">
                    {analysis.results.length} pattern{analysis.results.length !== 1 ? 's' : ''} detected
                  </span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {analysis.results.map((result: any, index: number) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-cosmic-500/20 text-cosmic-300 text-xs rounded-full"
                    >
                      {(result.type || 'Pattern').replace('_', ' ')} ({Math.round((result.confidence || 0) * 100)}%)
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {!isAnalyzing && results.length === 0 && !error && (
        <div className="text-center py-12">
          <Zap className="w-16 h-16 mx-auto mb-4 text-white/40" />
          <h4 className="text-xl font-medium text-white/60 mb-2">
            Ready for Analysis
          </h4>
          <p className="text-white/40 text-sm max-w-md mx-auto">
            Click the analyze button to detect mathematical patterns, fractals, 
            and harmonic relationships in the selected natural sound pattern.
          </p>
        </div>
      )}
    </div>
  );
};
