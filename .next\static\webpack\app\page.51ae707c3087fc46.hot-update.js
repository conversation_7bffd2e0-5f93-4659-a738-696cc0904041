"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/explorer/PatternAnalysisPanel.tsx":
/*!**********************************************************!*\
  !*** ./src/components/explorer/PatternAnalysisPanel.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PatternAnalysisPanel: function() { return /* binding */ PatternAnalysisPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Clock,Download,Layers,Loader2,RotateCcw,Settings,Share2,Target,TrendingUp,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ PatternAnalysisPanel auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst ProgressIndicator = (param)=>{\n    let { progress, isActive, label } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-8 h-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 rounded-full border-2 border-white/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute inset-0 rounded-full border-2 border-cosmic-400\",\n                        style: {\n                            background: \"conic-gradient(from 0deg, #8B5CF6 \".concat(progress * 3.6, \"deg, transparent \").concat(progress * 3.6, \"deg)\")\n                        },\n                        animate: {\n                            rotate: isActive ? 360 : 0\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: isActive ? Infinity : 0,\n                            ease: \"linear\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-2 rounded-full bg-cosmic-900 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-medium text-white\",\n                            children: [\n                                Math.round(progress),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium text-white\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-white/60\",\n                        children: isActive ? \"Processing...\" : progress === 100 ? \"Complete\" : \"Pending\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ProgressIndicator;\nconst AnalysisResult = (param)=>{\n    let { result, index } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getResultIcon = (type)=>{\n        switch(type.toLowerCase()){\n            case \"fibonacci\":\n                return _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"golden_ratio\":\n                return _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"fractal\":\n                return _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"harmonic\":\n                return _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n        }\n    };\n    const getConfidenceColor = (confidence)=>{\n        if (confidence >= 0.8) return \"text-green-400\";\n        if (confidence >= 0.6) return \"text-yellow-400\";\n        if (confidence >= 0.4) return \"text-orange-400\";\n        return \"text-red-400\";\n    };\n    const getConfidenceLabel = (confidence)=>{\n        if (confidence >= 0.8) return \"High\";\n        if (confidence >= 0.6) return \"Medium\";\n        if (confidence >= 0.4) return \"Low\";\n        return \"Very Low\";\n    };\n    const ResultIcon = getResultIcon(result.type || \"pattern\");\n    const confidence = result.confidence || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            delay: index * 0.1\n        },\n        className: \"bg-white/5 rounded-lg border border-white/10 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-cosmic-500/20 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResultIcon, {\n                                        className: \"w-4 h-4 text-cosmic-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-white text-sm capitalize\",\n                                            children: [\n                                                (result.type || \"Pattern\").replace(\"_\", \" \"),\n                                                \" Analysis\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-white/60\",\n                                            children: result.description || \"Mathematical pattern analysis\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"p-1 text-white/60 hover:text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                animate: {\n                                    rotate: isExpanded ? 180 : 0\n                                },\n                                transition: {\n                                    duration: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-sm mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white/70\",\n                                    children: \"Confidence\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium \".concat(getConfidenceColor(confidence)),\n                                            children: getConfidenceLabel(confidence)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white/60\",\n                                            children: [\n                                                Math.round(confidence * 100),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-white/10 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"h-2 rounded-full \".concat(confidence >= 0.8 ? \"bg-green-500\" : confidence >= 0.6 ? \"bg-yellow-500\" : confidence >= 0.4 ? \"bg-orange-500\" : \"bg-red-500\"),\n                                initial: {\n                                    width: 0\n                                },\n                                animate: {\n                                    width: \"\".concat(confidence * 100, \"%\")\n                                },\n                                transition: {\n                                    duration: 1,\n                                    delay: 0.5\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined),\n                result.metrics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-3 mb-3\",\n                    children: Object.entries(result.metrics).slice(0, 4).map((param)=>{\n                        let [key, value] = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-2 bg-white/5 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-white/60 capitalize mb-1\",\n                                    children: key.replace(/([A-Z])/g, \" $1\").toLowerCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: typeof value === \"number\" ? value < 0.01 ? value.toExponential(2) : value.toFixed(3) : String(value)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, key, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                confidence >= 0.6 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4 text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, undefined) : confidence >= 0.3 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 text-yellow-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-white/70\",\n                                    children: confidence >= 0.6 ? \"Pattern Detected\" : confidence >= 0.3 ? \"Weak Pattern\" : \"No Clear Pattern\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-white/50\",\n                            children: result.processingTime ? \"\".concat(result.processingTime, \"ms\") : \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                    children: isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        className: \"mt-4 pt-4 border-t border-white/10\",\n                        children: [\n                            result.metrics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"text-sm font-medium text-white/80 mb-2\",\n                                        children: \"Detailed Metrics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: Object.entries(result.metrics).map((param)=>{\n                                            let [key, value] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/60 capitalize\",\n                                                        children: [\n                                                            key.replace(/([A-Z])/g, \" $1\").toLowerCase(),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/80 font-mono\",\n                                                        children: typeof value === \"number\" ? value < 0.01 ? value.toExponential(3) : value.toFixed(4) : String(value)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 23\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 17\n                            }, undefined),\n                            result.algorithm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"text-sm font-medium text-white/80 mb-2\",\n                                        children: \"Algorithm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-white/70\",\n                                        children: result.algorithm\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 17\n                            }, undefined),\n                            result.parameters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"text-sm font-medium text-white/80 mb-2\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                        children: Object.entries(result.parameters).map((param)=>{\n                                            let [key, value] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/60\",\n                                                        children: [\n                                                            key,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white/80 font-mono\",\n                                                        children: String(value)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 23\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnalysisResult, \"FPNvbbHVlWWR4LKxxNntSxiIS38=\");\n_c1 = AnalysisResult;\nconst PatternAnalysisPanel = (param)=>{\n    let { analysisState, onAnalyze, onExportResults, onShareResults, className = \"\" } = param;\n    var _analysisSteps_getCurrentStep;\n    _s1();\n    const [analysisHistory, setAnalysisHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAnalyzing, results, error, progress } = analysisState;\n    // Store completed analyses in history\n    useEffect(()=>{\n        if (!isAnalyzing && results.length > 0 && progress === 100) {\n            setAnalysisHistory((prev)=>[\n                    {\n                        timestamp: Date.now(),\n                        results: [\n                            ...results\n                        ],\n                        id: \"analysis-\".concat(Date.now())\n                    },\n                    ...prev.slice(0, 4)\n                ]);\n        }\n    }, [\n        isAnalyzing,\n        results,\n        progress\n    ]);\n    const analysisSteps = [\n        {\n            label: \"Preprocessing Audio\",\n            threshold: 20\n        },\n        {\n            label: \"Frequency Analysis\",\n            threshold: 40\n        },\n        {\n            label: \"Pattern Detection\",\n            threshold: 60\n        },\n        {\n            label: \"Mathematical Analysis\",\n            threshold: 80\n        },\n        {\n            label: \"Confidence Calculation\",\n            threshold: 100\n        }\n    ];\n    const getCurrentStep = ()=>{\n        return analysisSteps.findIndex((step)=>progress < step.threshold);\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp).toLocaleTimeString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-cosmic-500/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-5 h-5 text-cosmic-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Pattern Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: \"Mathematical pattern detection and analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            !isAnalyzing && results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    onExportResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>onExportResults(\"json\"),\n                                            className: \"p-2 text-white/70 hover:text-white transition-colors\",\n                                            title: \"Export results\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    onShareResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onShareResults,\n                                        className: \"p-2 text-white/70 hover:text-white transition-colors\",\n                                        title: \"Share results\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-white/70 hover:text-white transition-colors\",\n                                title: \"Analysis settings\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    className: \"bg-white/5 rounded-lg p-4 border border-white/10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-white/80 mb-3\",\n                            children: \"Analysis Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-xs text-white/60 mb-1 block\",\n                                            children: \"Sensitivity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"range\",\n                                            min: \"0.1\",\n                                            max: \"1\",\n                                            step: \"0.1\",\n                                            defaultValue: \"0.7\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-xs text-white/60 mb-1 block\",\n                                            children: \"Window Size\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"w-full bg-white/10 border border-white/20 rounded px-2 py-1 text-xs text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"1024\",\n                                                    children: \"1024\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"2048\",\n                                                    children: \"2048\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"4096\",\n                                                    children: \"4096\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-white mb-1\",\n                                        children: isAnalyzing ? \"Analysis in Progress\" : \"Ready to Analyze\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-sm\",\n                                        children: isAnalyzing ? \"Step \".concat(getCurrentStep() + 1, \" of \").concat(analysisSteps.length, \": \").concat(((_analysisSteps_getCurrentStep = analysisSteps[getCurrentStep()]) === null || _analysisSteps_getCurrentStep === void 0 ? void 0 : _analysisSteps_getCurrentStep.label) || \"Processing\") : \"Click analyze to detect mathematical patterns in the audio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                onClick: onAnalyze,\n                                disabled: isAnalyzing,\n                                className: \"\\n              px-6 py-3 rounded-lg font-medium transition-all duration-200\\n              \".concat(isAnalyzing ? \"bg-white/10 text-white/50 cursor-not-allowed\" : \"bg-gradient-to-r from-cosmic-400 to-cosmic-500 text-white hover:from-cosmic-500 hover:to-cosmic-600 shadow-lg hover:shadow-xl\", \"\\n            \"),\n                                whileHover: !isAnalyzing ? {\n                                    scale: 1.05\n                                } : {},\n                                whileTap: !isAnalyzing ? {\n                                    scale: 0.95\n                                } : {},\n                                children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Analyzing...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Analyze Pattern\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, undefined),\n                    isAnalyzing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: analysisSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressIndicator, {\n                                progress: Math.max(0, Math.min(100, (progress - index * 20) / 20 * 100)),\n                                isActive: getCurrentStep() === index,\n                                label: step.label\n                            }, step.label, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 text-red-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-5 h-5 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium\",\n                                    children: \"Analysis Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-300 mt-1\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                lineNumber: 482,\n                columnNumber: 9\n            }, undefined),\n            !isAnalyzing && results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-white\",\n                        children: \"Analysis Results\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 500,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalysisResult, {\n                                result: result,\n                                index: index\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                lineNumber: 499,\n                columnNumber: 9\n            }, undefined),\n            analysisHistory.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-white\",\n                        children: \"Recent Analyses\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: analysisHistory.map((analysis)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/5 rounded-lg p-4 border border-white/10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white/60\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-white/80\",\n                                                        children: formatTimestamp(analysis.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-white/60\",\n                                                children: [\n                                                    analysis.results.length,\n                                                    \" pattern\",\n                                                    analysis.results.length !== 1 ? \"s\" : \"\",\n                                                    \" detected\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: analysis.results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 bg-cosmic-500/20 text-cosmic-300 text-xs rounded-full\",\n                                                children: [\n                                                    (result.type || \"Pattern\").replace(\"_\", \" \"),\n                                                    \" (\",\n                                                    Math.round((result.confidence || 0) * 100),\n                                                    \"%)\"\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, analysis.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                lineNumber: 511,\n                columnNumber: 9\n            }, undefined),\n            !isAnalyzing && results.length === 0 && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Clock_Download_Layers_Loader2_RotateCcw_Settings_Share2_Target_TrendingUp_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"w-16 h-16 mx-auto mb-4 text-white/40\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-xl font-medium text-white/60 mb-2\",\n                        children: \"Ready for Analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/40 text-sm max-w-md mx-auto\",\n                        children: \"Click the analyze button to detect mathematical patterns, fractals, and harmonic relationships in the selected natural sound pattern.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                        lineNumber: 550,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n                lineNumber: 545,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Cosmic\\\\src\\\\components\\\\explorer\\\\PatternAnalysisPanel.tsx\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(PatternAnalysisPanel, \"euv/FQDtzOJqpoRT4ZjYwkocbwE=\");\n_c2 = PatternAnalysisPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProgressIndicator\");\n$RefreshReg$(_c1, \"AnalysisResult\");\n$RefreshReg$(_c2, \"PatternAnalysisPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/explorer/PatternAnalysisPanel.tsx\n"));

/***/ })

});