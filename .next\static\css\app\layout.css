/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Dynamic Theming */
:root {
  --cosmic-primary: #667eea;
  --cosmic-secondary: #764ba2;
  --nature-ocean: #0ea5e9;
  --nature-forest: #16a34a;
  --raga-morning: #fbbf24;
  --raga-evening: #8b5cf6;
  
  /* Audio visualization colors */
  --waveform-color: #6366f1;
  --spectrum-color: #8b5cf6;
  --pattern-match-color: #16a34a;
  
  /* Animation durations */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
}

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply text-white;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11', "onum";
    font-variant-numeric: oldstyle-nums;
  }
  
  /* Focus styles for accessibility */
  *:focus {
    @apply outline-none ring-2 ring-cosmic-400 ring-offset-2 ring-offset-transparent;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-white/10 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-white/30 rounded-full hover:bg-white/50;
  }
}

/* Component styles */
@layer components {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl;
  }
  
  .glass-strong {
    @apply bg-white/20 backdrop-blur-lg border border-white/30 rounded-xl;
  }
  
  /* Button variants */
  .btn-primary {
    @apply bg-gradient-to-r from-cosmic-500 to-cosmic-600 hover:from-cosmic-600 hover:to-cosmic-700 
           text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 
           shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 
           focus:ring-2 focus:ring-cosmic-400 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-white/10 hover:bg-white/20 text-white font-medium px-6 py-3 rounded-lg 
           transition-all duration-300 border border-white/20 hover:border-white/30
           backdrop-blur-sm hover:backdrop-blur-md;
  }
  
  .btn-nature {
    @apply bg-gradient-to-r from-nature-forest to-nature-ocean hover:from-green-600 hover:to-blue-600
           text-white font-medium px-6 py-3 rounded-lg transition-all duration-300
           shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }
  
  /* Audio control styles */
  .audio-control {
    @apply w-12 h-12 rounded-full bg-white/20 hover:bg-white/30 
           flex items-center justify-center transition-all duration-300
           border border-white/20 hover:border-white/40 backdrop-blur-sm;
  }
  
  .audio-control:hover {
    @apply transform scale-105 shadow-lg;
  }
  
  .audio-control:active {
    @apply transform scale-95;
  }
  
  /* Waveform visualization */
  .waveform-container {
    @apply relative w-full h-24 bg-black/20 rounded-lg overflow-hidden border border-white/10;
  }
  
  .waveform-bar {
    @apply bg-gradient-to-t from-cosmic-500 to-cosmic-300 rounded-sm transition-all duration-100;
  }
  
  /* Pattern visualization */
  .pattern-node {
    @apply w-4 h-4 rounded-full bg-cosmic-400 border-2 border-white/50 
           transition-all duration-300 hover:scale-125 cursor-pointer;
  }
  
  .pattern-connection {
    @apply stroke-cosmic-300 stroke-2 opacity-60 hover:opacity-100 transition-opacity duration-300;
  }
  
  /* Card styles */
  .nature-card {
    @apply glass p-6 hover:bg-white/15 transition-all duration-300 
           transform hover:-translate-y-1 hover:shadow-2xl cursor-pointer
           border-l-4 border-l-nature-ocean;
  }
  
  .music-card {
    @apply glass p-6 hover:bg-white/15 transition-all duration-300
           transform hover:-translate-y-1 hover:shadow-2xl cursor-pointer
           border-l-4 border-l-raga-evening;
  }
  
  /* Loading animations */
  .loading-pulse {
    @apply animate-pulse bg-gradient-to-r from-white/10 to-white/20 rounded;
  }
  
  .loading-spin {
    @apply animate-spin-slow;
  }
  
  /* Text styles */
  .text-gradient-cosmic {
    @apply bg-gradient-to-r from-cosmic-300 to-cosmic-500 bg-clip-text text-transparent;
  }
  
  .text-gradient-nature {
    @apply bg-gradient-to-r from-nature-forest to-nature-ocean bg-clip-text text-transparent;
  }
  
  .text-gradient-raga {
    @apply bg-gradient-to-r from-raga-morning to-raga-evening bg-clip-text text-transparent;
  }
}

/* Utility classes */
@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }
  
  /* Responsive text utilities */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }
  
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }
  
  .text-responsive-base {
    @apply text-base sm:text-lg;
  }
  
  .text-responsive-lg {
    @apply text-lg sm:text-xl;
  }
  
  .text-responsive-xl {
    @apply text-xl sm:text-2xl;
  }
  
  /* Spacing utilities */
  .space-y-responsive {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }
  
  /* Grid utilities */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Audio visualization specific styles */
.frequency-bar {
  transition: height 50ms ease-out;
}

.pattern-highlight {
  @apply ring-2 ring-yellow-400 ring-opacity-75 animate-pulse;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass {
    @apply bg-black/80 border-white;
  }
  
  .btn-primary {
    @apply bg-blue-600 border-2 border-white;
  }
}

