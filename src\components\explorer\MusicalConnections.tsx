'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Music,
  Clock,
  Play,
  ExternalLink,
  ChevronDown,
  ChevronRight,
  BookOpen,
  MapPin
} from 'lucide-react';

import { 
  MusicalConnection, 
  MusicalTradition, 
  MusicalConnectionType,
  Emotion,
  TimeOfDay 
} from '@/types/NaturalPattern.types';

interface MusicalConnectionsProps {
  connections: MusicalConnection[];
  className?: string;
}

interface ConnectionCardProps {
  connection: MusicalConnection;
  onPlayExample?: (exampleId: string) => void;
  isExpanded?: boolean;
  onToggleExpanded?: () => void;
}

// Tradition icons and colors
const traditionStyles = {
  [MusicalTradition.HINDUSTANI_CLASSICAL]: {
    icon: '🎵',
    color: 'from-orange-500/20 to-red-500/20 border-orange-400/30',
    textColor: 'text-orange-300',
  },
  [MusicalTradition.CARNATIC_CLASSICAL]: {
    icon: '🎶',
    color: 'from-red-500/20 to-pink-500/20 border-red-400/30',
    textColor: 'text-red-300',
  },
  [MusicalTradition.WESTERN_CLASSICAL]: {
    icon: '🎼',
    color: 'from-blue-500/20 to-indigo-500/20 border-blue-400/30',
    textColor: 'text-blue-300',
  },
  [MusicalTradition.JAZZ]: {
    icon: '🎷',
    color: 'from-yellow-500/20 to-amber-500/20 border-yellow-400/30',
    textColor: 'text-yellow-300',
  },
  [MusicalTradition.FOLK]: {
    icon: '🪕',
    color: 'from-green-500/20 to-emerald-500/20 border-green-400/30',
    textColor: 'text-green-300',
  },
  [MusicalTradition.CONTEMPORARY]: {
    icon: '🎧',
    color: 'from-purple-500/20 to-pink-500/20 border-purple-400/30',
    textColor: 'text-purple-300',
  },
  [MusicalTradition.ELECTRONIC]: {
    icon: '🎛️',
    color: 'from-cyan-500/20 to-blue-500/20 border-cyan-400/30',
    textColor: 'text-cyan-300',
  },
  [MusicalTradition.WORLD_MUSIC]: {
    icon: '🌍',
    color: 'from-emerald-500/20 to-green-500/20 border-emerald-400/30',
    textColor: 'text-emerald-300',
  },
};

// Connection type descriptions
const connectionTypeDescriptions = {
  [MusicalConnectionType.DIRECT_SAMPLING]: 'Direct audio sampling or field recording usage',
  [MusicalConnectionType.RHYTHMIC_INSPIRATION]: 'Rhythmic patterns and timing similarities',
  [MusicalConnectionType.MELODIC_MIMICRY]: 'Melodic contours and phrase structures',
  [MusicalConnectionType.HARMONIC_REFLECTION]: 'Harmonic structures and chord progressions',
  [MusicalConnectionType.TEXTURAL_SIMILARITY]: 'Sound texture and tonal qualities',
  [MusicalConnectionType.CONCEPTUAL_INSPIRATION]: 'Conceptual and emotional connections',
};

// Emotion icons
const emotionIcons = {
  [Emotion.JOY]: '☀️',
  [Emotion.PEACE]: '🕊️',
  [Emotion.MELANCHOLY]: '🌧️',
  [Emotion.EXCITEMENT]: '⚡',
  [Emotion.CONTEMPLATION]: '🧘',
  [Emotion.DEVOTION]: '💝',
  [Emotion.LONGING]: '🌙',
  [Emotion.CELEBRATION]: '🎉',
  [Emotion.MYSTERY]: '🔮',
  [Emotion.POWER]: '⚡',
};

// Time of day icons
const timeIcons = {
  [TimeOfDay.DAWN]: '🌅',
  [TimeOfDay.MORNING]: '🌄',
  [TimeOfDay.AFTERNOON]: '☀️',
  [TimeOfDay.EVENING]: '🌇',
  [TimeOfDay.NIGHT]: '🌙',
  [TimeOfDay.MIDNIGHT]: '🌌',
};

const ConnectionCard: React.FC<ConnectionCardProps> = ({
  connection,
  onPlayExample,
  isExpanded = false,
  onToggleExpanded,
}) => {
  const traditionStyle = traditionStyles[connection.tradition];

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`
        rounded-xl border backdrop-blur-sm transition-all duration-300
        bg-gradient-to-br ${traditionStyle.color}
        hover:shadow-lg hover:shadow-white/10
      `}
    >
      <div className="p-4">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{traditionStyle.icon}</div>
            <div>
              <h4 className="font-semibold text-white text-sm">
                {connection.title}
              </h4>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`text-xs ${traditionStyle.textColor} capitalize`}>
                  {connection.tradition.replace('_', ' ')}
                </span>
                <span className="text-white/40">•</span>
                <span className="text-xs text-white/60 capitalize">
                  {connection.type.replace('_', ' ')}
                </span>
              </div>
            </div>
          </div>

          {onToggleExpanded && (
            <button
              onClick={onToggleExpanded}
              className="p-1 text-white/60 hover:text-white transition-colors"
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>
          )}
        </div>

        {/* Description */}
        <p className="text-white/80 text-sm mb-3 line-clamp-2">
          {connection.description}
        </p>

        {/* Metadata */}
        <div className="flex flex-wrap gap-2 mb-3">
          <div className="flex items-center space-x-1 text-xs text-white/60">
            <BookOpen className="w-3 h-3" />
            <span>{connection.artist}</span>
          </div>

          {connection.culturalContext.timeAssociation && (
            <div className="flex items-center space-x-1 text-xs text-white/60">
              <Clock className="w-3 h-3" />
              <span className="capitalize">{connection.culturalContext.timeAssociation.replace('_', ' ')}</span>
            </div>
          )}

          <div className="flex items-center space-x-1 text-xs text-white/60">
            <MapPin className="w-3 h-3" />
            <span>{connection.culturalContext.region}</span>
          </div>
        </div>

        {/* Emotional and temporal associations */}
        <div className="flex flex-wrap gap-1 mb-3">
          <span
            className="inline-flex items-center space-x-1 px-2 py-1 bg-white/10 rounded-full text-xs text-white/70"
            title={connection.culturalContext.emotionalContext.primary}
          >
            <span>{emotionIcons[connection.culturalContext.emotionalContext.primary]}</span>
            <span className="capitalize">{connection.culturalContext.emotionalContext.primary}</span>
          </span>

          {connection.culturalContext.emotionalContext.secondary?.map((emotion) => (
            <span
              key={emotion}
              className="inline-flex items-center space-x-1 px-2 py-1 bg-white/10 rounded-full text-xs text-white/60"
              title={emotion}
            >
              <span>{emotionIcons[emotion]}</span>
              <span className="capitalize">{emotion}</span>
            </span>
          ))}

          {connection.culturalContext.timeAssociation && (
            <span
              className="inline-flex items-center space-x-1 px-2 py-1 bg-white/10 rounded-full text-xs text-white/70"
              title={connection.culturalContext.timeAssociation}
            >
              <span>{timeIcons[connection.culturalContext.timeAssociation]}</span>
              <span className="capitalize">{connection.culturalContext.timeAssociation.replace('_', ' ')}</span>
            </span>
          )}
        </div>

        {/* Similarity score */}
        <div className="mb-3">
          <div className="flex items-center justify-between text-xs mb-1">
            <span className="text-white/60">Pattern Similarity</span>
            <span className={traditionStyle.textColor}>
              {typeof connection.similarity === 'number'
                ? `${Math.round(connection.similarity * 100)}%`
                : `${Math.round(connection.similarity.overallScore * 100)}%`
              }
            </span>
          </div>
          <div className="w-full bg-white/10 rounded-full h-2">
            <motion.div
              className={`h-2 rounded-full bg-gradient-to-r ${traditionStyle.color.replace('/20', '/60')}`}
              initial={{ width: 0 }}
              animate={{
                width: `${typeof connection.similarity === 'number'
                  ? connection.similarity * 100
                  : connection.similarity.overallScore * 100
                }%`
              }}
              transition={{ duration: 1, delay: 0.2 }}
            />
          </div>
        </div>

        {/* Audio/Video links */}
        {(connection.audioUrl || connection.videoUrl || connection.spotifyUrl || connection.youtubeUrl) && (
          <div className="space-y-2">
            <h5 className="text-sm font-medium text-white/80">Listen & Watch</h5>
            <div className="flex flex-wrap gap-2">
              {connection.audioUrl && (
                <button
                  onClick={() => onPlayExample?.(connection.audioUrl!)}
                  className="flex items-center space-x-1 px-3 py-1 bg-white/10 hover:bg-white/20 rounded-full text-xs text-white/80 transition-colors"
                  title="Play audio"
                >
                  <Play className="w-3 h-3" />
                  <span>Audio</span>
                </button>
              )}

              {connection.videoUrl && (
                <a
                  href={connection.videoUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-1 px-3 py-1 bg-white/10 hover:bg-white/20 rounded-full text-xs text-white/80 transition-colors"
                  title="Watch video"
                >
                  <ExternalLink className="w-3 h-3" />
                  <span>Video</span>
                </a>
              )}

              {connection.spotifyUrl && (
                <a
                  href={connection.spotifyUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-1 px-3 py-1 bg-green-500/20 hover:bg-green-500/30 rounded-full text-xs text-green-300 transition-colors"
                  title="Listen on Spotify"
                >
                  <ExternalLink className="w-3 h-3" />
                  <span>Spotify</span>
                </a>
              )}

              {connection.youtubeUrl && (
                <a
                  href={connection.youtubeUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-1 px-3 py-1 bg-red-500/20 hover:bg-red-500/30 rounded-full text-xs text-red-300 transition-colors"
                  title="Watch on YouTube"
                >
                  <ExternalLink className="w-3 h-3" />
                  <span>YouTube</span>
                </a>
              )}
            </div>
          </div>
        )}

        {/* Expanded content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 pt-4 border-t border-white/10"
            >
              {/* Cultural context details */}
              <div className="mb-4">
                <h5 className="text-sm font-medium text-white/80 mb-2">Cultural Context</h5>
                <div className="space-y-2 text-sm text-white/70">
                  <p><strong>Historical Significance:</strong> {connection.culturalContext.historicalSignificance}</p>
                  <p><strong>Modern Relevance:</strong> {connection.culturalContext.modernRelevance}</p>
                  {connection.culturalContext.ritualContext && (
                    <p><strong>Ritual Context:</strong> {connection.culturalContext.ritualContext}</p>
                  )}
                  {connection.culturalContext.seasonAssociation && (
                    <p><strong>Season:</strong> {connection.culturalContext.seasonAssociation}</p>
                  )}
                </div>
              </div>

              {/* Technical analysis */}
              {connection.technicalAnalysis && (
                <div className="space-y-2">
                  <h5 className="text-sm font-medium text-white/80">Technical Analysis</h5>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    {Object.entries(connection.technicalAnalysis).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="text-white/60 capitalize">{key.replace(/([A-Z])/g, ' $1')}:</span>
                        <span className="text-white/80">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export const MusicalConnections: React.FC<MusicalConnectionsProps> = ({
  connections,
  className = '',
}) => {
  const [expandedCards, setExpandedCards] = useState<Set<number>>(new Set());
  const [filterTradition, setFilterTradition] = useState<MusicalTradition | 'all'>('all');
  const [sortBy, setSortBy] = useState<'similarity' | 'tradition' | 'type'>('similarity');

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedCards);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedCards(newExpanded);
  };

  // Filter and sort connections
  const processedConnections = connections
    .filter(connection =>
      filterTradition === 'all' || connection.tradition === filterTradition
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'similarity':
          const aScore = typeof a.similarity === 'number' ? a.similarity : a.similarity.overallScore;
          const bScore = typeof b.similarity === 'number' ? b.similarity : b.similarity.overallScore;
          return bScore - aScore;
        case 'tradition':
          return a.tradition.localeCompare(b.tradition);
        case 'type':
          return a.type.localeCompare(b.type);
        default:
          return 0;
      }
    });

  if (connections.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <Music className="w-12 h-12 mx-auto mb-4 text-white/40" />
        <h4 className="text-lg font-medium text-white/60 mb-2">
          No Musical Connections
        </h4>
        <p className="text-white/40 text-sm">
          Musical connections will appear here when patterns are analyzed
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header and Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-white mb-1">
            Musical Connections
          </h3>
          <p className="text-white/60 text-sm">
            {processedConnections.length} connection{processedConnections.length !== 1 ? 's' : ''} found
          </p>
        </div>

        <div className="flex items-center space-x-2">
          {/* Filter by tradition */}
          <select
            value={filterTradition}
            onChange={(e) => setFilterTradition(e.target.value as any)}
            className="bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-sm text-white"
          >
            <option value="all">All Traditions</option>
            {Object.values(MusicalTradition).map(tradition => (
              <option key={tradition} value={tradition}>
                {tradition.replace('_', ' ')}
              </option>
            ))}
          </select>

          {/* Sort by */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-sm text-white"
          >
            <option value="confidence">Confidence</option>
            <option value="tradition">Tradition</option>
            <option value="type">Type</option>
          </select>
        </div>
      </div>

      {/* Connection type legend */}
      <div className="flex flex-wrap gap-2 p-3 bg-white/5 rounded-lg">
        <span className="text-xs text-white/60">Connection Types:</span>
        {Object.entries(connectionTypeDescriptions).map(([type, description]) => (
          <span
            key={type}
            className="px-2 py-1 bg-white/10 rounded-full text-xs text-white/70 capitalize"
            title={description}
          >
            {type.replace('_', ' ')}
          </span>
        ))}
      </div>

      {/* Connections Grid */}
      <div className="grid gap-4">
        <AnimatePresence>
          {processedConnections.map((connection, index) => (
            <ConnectionCard
              key={`${connection.title}-${index}`}
              connection={connection}
              isExpanded={expandedCards.has(index)}
              onToggleExpanded={() => toggleExpanded(index)}
              onPlayExample={(exampleId) => {
                console.log('Playing example:', exampleId);
                // This would integrate with the audio engine
              }}
            />
          ))}
        </AnimatePresence>
      </div>

      {processedConnections.length === 0 && filterTradition !== 'all' && (
        <div className="text-center py-8">
          <Music className="w-8 h-8 mx-auto mb-2 text-white/40" />
          <p className="text-white/60 text-sm">
            No connections found for {filterTradition.replace('_', ' ')} tradition
          </p>
        </div>
      )}
    </div>
  );
};
